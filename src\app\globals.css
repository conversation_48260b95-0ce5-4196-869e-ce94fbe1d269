@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Switzer-Regular";
  scroll-behavior: smooth;
}

@font-face {
  font-family: "Switzer-Regular";
  src: url("/assets/fonts/Switzer-Regular.woff2") format("woff2"),
    url("/assets/fonts/Switzer-Regular.woff") format("woff"),
    url("/assets/fonts/Switzer-Regular.ttf") format("truetype");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.menu-hidden {
  display: none;
}

.menu-visible {
  display: block;
}

.bg-overlay {
  background-image: linear-gradient(
      180deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0.8) 72.22%,
      rgba(250, 245, 245, 0.1) 148.9%
    ),
    /* radial-gradient(transparent 5%, white 85%), */
      url("https://res.cloudinary.com/dbz8cdpis/image/upload/v1734497254/arthalpha/67809cff02b9d0e0b398ce14abbe1b6c_n6tbq6.png");
  background-size: cover;
  background-position: center;
  /* box-shadow: 0px 4px 4px 0px #00000040; */

  /* height: 60vh; */
  /* min-height: 100vh; */
}

.row {
  @apply flex flex-row items-center;
}

.btn-border {
  @apply text-primary rounded-xl border-[rgba(190,18,60,0.1)] h-10 hover:bg-primary hover:text-white  mt-4 px-8  font-bold;
}

.shadow {
  /* @apply box-shadow: 0px 0px 27.07px -1.46px rgba(17, 24, 39, 0.05); */
  box-shadow: 0px 0px 27.07px -1.46px rgba(17, 24, 39, 0.05);
}

.image-hover-white {
  transition: filter 0.3s ease;
}

.image-hover-white:hover {
  filter: brightness(0) invert(1);
}

.text-sec-normal {
  @apply text-lg text-secondary max-md:text-base;
}

.heading-large {
  @apply text-5xl font-bold max-md:text-3xl max-lg:text-4xl;
}

.heading {
  @apply text-4xl font-bold max-md:text-2xl max-lg:text-2xl;
}

.heading-small {
  @apply text-3xl font-semibold max-md:text-2xl;
}
.sub-heading {
  @apply text-[#545454] text-lg max-md:text-base;
}

.hero-heading {
  @apply text-center max-md:text-[28px] heading-large py-16 max-lg:py-8 max-md:py-8 pt-40 max-lg:pt-28 max-md:pt-24;
}

.card {
  @apply rounded-[30px] max-md:rounded-[20px];
}

.shadow-small {
  box-shadow: none;
}

.box-shadow {
  border-bottom-width: 1px;
  border-color: #e2e2e2;
  border-left-width: 1px;
  border-right-width: 1px;
  border-style: solid;
  border-top-width: 1px;
  box-shadow: inset 0 4px 4px -2px #0000001f, inset 0 -4px 4px -2px #0000001f,
    inset 0 0 3px #0000003d, 0 0 0 1px #00000014,
    0 7px 11.399999618530273px -7px #1f1c1c26, 0 16px 24px -7px #1f1c1c14;
}

.card {
  @apply p-0 border-none  rounded-2xl  flex-1 shadow-[0px_0px_27.07px_-1.46px_rgba(17,24,39,0.05)];
}
.missionVisionCard {
  @apply p-0 rounded-3xl border-none  bg-[linear-gradient(299.83deg,#F7F8FA_12.44%,#FFFFFF_100%)] shadow-[0px_0px_0px_0px_rgba(255,255,255,1),0px_0px_0px_6px_rgba(241,243,250,1),0px_0px_0px_0px_rgba(0,0,0,0)];
}

.titleCard {
  @apply p-0 rounded-3xl border-none  bg-[linear-gradient(299.83deg,#F7F8FA_12.44%,#FFFFFF_100%)] shadow-[0px_0px_0px_0px_rgba(255,255,255,1),0px_0px_0px_4px_rgba(241,243,250,1),0px_0px_0px_0px_rgba(0,0,0,0)];
}
.valusCard {
  @apply shadow-[4px_12px_40px_6px_rgba(0,0,0,0.09)] p-0 rounded-3xl border-none;
}

/* In your global CSS or component CSS file */
.custom-modal-backdrop::before {
  background: rgba(0, 0, 0, 0.6); /* Adjust transparency as needed */
}

.appearance-none {
  display: none;
}

/* scroll snap */
/* Enable scroll-snap on the container */
.scroll-container {
  scroll-snap-type: y mandatory; /* Vertical scroll snap */
  overflow-y: auto; /* Allow scrolling */
  height: 100vh; /* Ensure the container takes up the full viewport height */
  display: flex;
  flex-direction: column; /* Stack sections vertically */
}

/* Apply snap to each section */
.scroll-area {
  scroll-snap-align: start; /* Snap sections to the start of the viewport */
  height: 100vh; /* Make each section take full viewport height */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

@media (min-width: 767px) {
  .more {
    left: 50%;
  }
}

.more {
  position: absolute;
  bottom: 40px;
  left: 50%;
  margin-left: -15px;
  width: 30px;
  height: 55px;
  border: 2px solid #800020;
  z-index: 2;
  background-size: cover;
  border-radius: 25px;
}

.more:after {
  content: " ";
  position: absolute;
  width: 4px;
  height: 8px;
  background: #800020;
  display: block;
  top: 30%;
  left: 50%;
  margin-left: -2px;
  border-radius: 100px;

  /* Apply animation */
  -webkit-animation: cursor 1.5s infinite ease-in-out;
  animation: cursor 1.5s infinite ease-in-out;
}

/* Keyframes for the animation */
@keyframes cursor {
  0% {
    transform: translateY(0); /* Start at the initial position */
  }
  50% {
    transform: translateY(-10px); /* Move up */
  }
  100% {
    transform: translateY(0); /* Return to the initial position */
  }
}

@-webkit-keyframes cursor {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

/* Make the parent container scrollable with snapping enabled */
.landing-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* Make sure the container takes up the full viewport height */
  overflow-y: scroll; /* Enable vertical scrolling */
  scroll-snap-type: y mandatory; /* Enable vertical scroll snapping */
  scroll-behavior: smooth; /* Optional: Enable smooth scrolling */
}

/* Ensure each section inside the container snaps properly */
@media (min-width: 768px) {
  .snap-section {
    scroll-snap-align: start; /* Align sections to the start of the container */
    flex-shrink: 0; /* Prevent sections from shrinking */
  }
}

.mobileNavItem {
  @apply items-center justify-start rounded-md transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 p-1 text-left flex;
}

.heroText {
  @apply text-8xl max-md:text-center font-bold leading-[60px] tracking-tight max-lg:text-5xl max-md:text-3xl;
  color: #000;
  font-style: normal;
  line-height: 130%; /* 130px */
  letter-spacing: 0.025em;
}
.bg-hero {
  background-size: 100%;
}
@media (max-width: 767px) {
  .bg-hero {
    background-size: cover;
  }
}
.bg-white-7 {
  background-color: rgb(255 255 255 / 0.07);
}

.primary-gradient {
  background: linear-gradient(
    264deg,
    #c40031 0%,
    #8c0124 65.68%,
    #480314 98.94%
  );
}
.cardInvest {
  width: 20em;
  @apply bg-[#FAF5F5];
  border-radius: 2em;

  padding: 2em 1em;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: 0 1em;
}

.card__image {
  width: 10em;
  height: 10em;
  border-radius: 50%;
  @apply border-primary border-[4px];
  padding: 3px;
  margin-bottom: 1em;
}

.card__image img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.card__content {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.card__title {
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
  top: 0.2em;
}

.card__text {
  text-align: center;
  font-size: 1.1rem;
  margin: 1em 0;
}

/* Mobile PDF Carousel Styles */
@media (max-width: 767px) {
  .pdf-viewer-container {
    /* Allow vertical scrolling but prevent horizontal stretching */
    touch-action: pan-y pinch-zoom;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* Prevent horizontal overscroll behavior */
    overscroll-behavior-x: none;
    -webkit-overscroll-behavior-x: none;
    /* Create a stacking context to isolate events */
    position: relative;
    z-index: 1;
    /* Prevent any overflow that might affect parent elements */
    contain: layout style paint;
  }

  .pdf-viewer-container .rpv-core__viewer {
    /* Ensure single page display on mobile */
    overflow: hidden !important;
    /* Smooth transitions for page changes */
    transition: all 0.3s ease-in-out;
  }

  .pdf-viewer-container .rpv-core__page-layer {
    /* Center the page and ensure it fits mobile screen */
    display: flex !important;
    justify-content: center !important;
    align-items: flex-start !important;
    width: 100% !important;
    max-width: 100vw !important;
    /* Smooth page transitions */
    transition: transform 0.3s ease-in-out, opacity 0.2s ease-in-out;
  }

  .pdf-viewer-container .rpv-core__canvas-layer canvas {
    /* Ensure PDF pages fit mobile screen width */
    max-width: 100% !important;
    height: auto !important;
    /* Smooth rendering */
    transition: opacity 0.2s ease-in-out;
  }

  /* Hide scrollbars on mobile for cleaner carousel look */
  .pdf-viewer-container .rpv-core__viewer::-webkit-scrollbar {
    display: none;
  }

  .pdf-viewer-container .rpv-core__viewer {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Prevent text selection during swipe */
  .pdf-viewer-container * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth page change animation */
  .pdf-viewer-container .rpv-core__page-layer.changing {
    opacity: 0.7;
    transform: scale(0.98);
  }
}

/* Presentation Mode Styles - Simplified */
.pdf-presentation-mode {
  background: black !important;
}

/* Fullscreen presentation touch handling */
.fullscreen-presentation {
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
  overflow: hidden;
}

/* PDF container with zoom support */
.fullscreen-presentation .rpv-core__viewer {
  overflow: visible !important;
}

.fullscreen-presentation .rpv-core__page-layer {
  overflow: visible !important;
}

/* Landscape orientation specific styles */
@media screen and (orientation: landscape) {
  .pdf-presentation-mode .rpv-core__canvas-layer canvas {
    /* Optimize for landscape viewing */
    max-width: 90vw !important;
    max-height: 85vh !important;
  }

  /* Hide landscape indicator when already in landscape */
  .pdf-presentation-mode .landscape-indicator {
    display: none !important;
  }
}

/* Portrait orientation fallback */
@media screen and (orientation: portrait) {
  .pdf-presentation-mode .rpv-core__canvas-layer canvas {
    /* Adjust for portrait if landscape lock fails */
    max-width: 95vw !important;
    max-height: 70vh !important;
  }
}
