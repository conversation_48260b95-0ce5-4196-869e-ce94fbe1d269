"use client";
import React, { useState, useEffect } from "react";

const ConflictofInterestPolicy = (props: any) => {
  const docUrl = props.url;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Set a timeout to simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // 2 seconds

    // Cleanup the timer if the component is unmounted
    return () => clearTimeout(timer);
  }, []);

  // if (isLoading) {
  //   return (
  //     <div className="h-[100vh] bg-white absolute top-0 w-screen">
  //       {/* Replace with your loader component or animation */}
  //       <div className="loader w-[90%] h-[100vh] flex items-center justify-center">
  //         <Spinner size="lg" />
  //       </div>
  //     </div>
  //   );
  // }
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        height: "100vh",
        background: "white",
      }}
    >
      <iframe
        src={`https://docs.google.com/gview?url=${docUrl}&embedded=true`}
        style={{
          width: "100%",
          border: "none",
          height: "100%",
          background: "white",
        }}
        title="Document Viewer"
      ></iframe>
    </div>
  );
};

export default ConflictofInterestPolicy;
