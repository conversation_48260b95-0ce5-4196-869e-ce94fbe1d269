"use client";
import { Input } from "@nextui-org/input";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@nextui-org/react";
const NewsUpdate = () => {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState("");
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch(`${API_BASE_URL}/subscribe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        setStatus("Email sent successfully!");
        setEmail("");
        if (result?.success) {
          onOpen();
        }
      } else {
        setStatus(result.error || "Failed to send email.");
      }
    } catch (error) {
      console.error("Error:", error);
      setStatus("An error occurred.");
    }
  };
  return (
    <div>
      <div className="relative rounded-tr-[3rem] rounded-bl-[3rem] w-full h-full py-12 max-md:py-6 flex items-center justify-center primary-gradient">
        <div className="absolute top-0 right-0 w-64 h-64">
          <img
            src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/Gradient_zcuwdx.svg"
            alt=""
            width="200"
            height="200"
            className="  w-72 h-72 rounded-tr-[3rem] "
          />
        </div>

        <div className="relative   bg-white-7 border border-white/100 backdrop-blur-md rounded-[3rem] px-6 py-12 max-md:py-10 max-md:mx-8 text-center max-w-lg w-full text-white shadow-lg">
          <div className="flex justify-center mb-4">
            <img
              src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360366/arthalpha/thank_u2d1wu.svg"
              alt=""
              width="200"
              height="200"
            />
          </div>
          <h2 className="text-3xl font-bold">Never Miss an Update</h2>

          <p className="mt-4 text-base px-8">
            Sign up for our newsletter to receive regular updates, industry
            trends, & expert insights directly in your inbox.
            {/* <br />
          We will be in touch and contact you soon. */}
          </p>

          <div className=" flex flex-row items-center gap-3 mt-4 shadow-none">
            <Input
              type="email"
              // label="Email"
              placeholder="Email ID"
              // labelPlacement="outside"
              className=" rounded-md shadow-none h-12"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              style={{
                paddingRight: 0,
                boxShadow: "none",
                backgroundColor: "red",
              }}
              size="lg"
              endContent={
                <Button
                  className="bg-primary text-white -mr-2"
                  onClick={handleSubmit}
                >
                  Register
                </Button>
              }
            />
          </div>
        </div>
      </div>

      <div>
        <Modal
          isDismissable={false}
          isKeyboardDismissDisabled={true}
          isOpen={isOpen}
          backdrop="blur"
          onOpenChange={onOpenChange}
          placement="center"
          className="w-[80%] h-[80%] min-w-[80%] min-h-[80%] max-md:w-[95%] max-md:h-[70%] max-md:min-w-[95%] max-md:min-h-[70%] rounded-[3rem]"
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalBody className="rounded-[3rem]" style={{ padding: 0 }}>
                  <div className="relative rounded-[3rem] w-full h-full py-12 max-md:py-6 flex items-center justify-center  primary-gradient">
                    <div className="absolute top-6 right-0 w-64 h-64 rounded-bl-full rounded-[3rem]">
                      <img
                        src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/Gradient_zcuwdx.svg"
                        alt=""
                        width="200"
                        height="200"
                        className="  w-72 h-72 "
                      />
                    </div>

                    <div className="relative   bg-white-7 border border-white/100 backdrop-blur-md rounded-[3rem] px-6 py-12 max-md:py-10 max-md:mx-8 text-center max-w-lg w-full text-white shadow-lg">
                      <h2 className="text-3xl font-bold">THANK YOU!</h2>

                      <div className="flex justify-center mt-4">
                        <img
                          src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/image_26_i4zdma.png"
                          alt=""
                          width="200"
                          height="200"
                        />
                      </div>

                      <p className="mt-4 text-lg px-12">
                        Thank you for subscribing!
                        <br />
                        We look forward to sharing updates and insights with
                        you.
                      </p>

                      <Button
                        className="text-black bg-white hover:bg-white hover:text-black rounded-xl font-bold h-9 mt-4"
                        onClick={onClose}
                      >
                        Close
                      </Button>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
};

export default NewsUpdate;
