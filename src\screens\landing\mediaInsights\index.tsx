"use client";
import { useState } from "react";
import MediaCard from "./mediaCard";
import { Button } from "@/components/ui/button";
import RecentProduct from "./mediaCarousel";

// const data = ["All", "News", "Blog", "Articles", "Quotes"];

const data = ["All", "News", "Articles"];

const LandingMediaInsights = () => {
  const [mediaType, setMediaType] = useState("All");
  const [isActive, setIsActive] = useState(false);
  return (
    <>
      <div>
        <div>
          <p className="heading text-center font-bold mt-32 max-md:mt-12 max-lg:mt-20">
            Media Insights
          </p>
        </div>
        <div className="gap-3 space-x-2 space-y-2 mt-6">
          {data.map((item, index) => (
            // <div>

            <Button
              key={index}
              className={
                mediaType == item
                  ? " text-white rounded-full fill-primary font-bold "
                  : " rounded-full border-primary bg-none text-primary hover:bg-primary hover:text-white"
              }
              variant={mediaType == item ? "default" : "outline"}
              onClick={() => setMediaType(item)}
              disabled={
                item == "All" || item == "Articles" || item == "News"
                  ? false
                  : true
              }
            >
              {item}
            </Button>
          ))}
        </div>
        {/* <MediaCard mediaType={mediaType} /> */}
        <RecentProduct mediaType={mediaType} />
      </div>
    </>
  );
};

export default LandingMediaInsights;
