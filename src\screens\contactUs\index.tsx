"use client";

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import ContactForm from "./contactForn";

const ContactHero = () => {
  if (typeof window === "undefined") {
    // Return a server-safe fallback if rendered on the server test
    return (
      <div className="grid grid-cols-2 max-md:grid-cols-1 h-[40vh] max-md:mt-12 bg-overlay bg-white">
        <div className="flex flex-row items-center justify-center h-[40vh] w-full col-span-2 py-24">
          <div className="max-md:pt-16 col-span-2 text-center">
            <p className="text-5xl max-md:text-center font-bold leading-[60px] tracking-tight max-lg:text-4xl max-md:text-[40px]">
              Contact Us
            </p>
          </div>
        </div>
      </div>
    );
  }

  const searchParams = useSearchParams();
  const scrollFlag = searchParams.get("scroll");

  useEffect(() => {
    if (scrollFlag === "true") {
      window.scrollTo({ top: 340, behavior: "smooth" });
    }
  }, [scrollFlag]);

  const backgroundUrl = "/assets/heroImages/contact.png";
  return (
    <>
      <div
        className="grid grid-cols-2 max-md:grid-cols-1 h-[40vh] max-md:h-[25vh]  bg-white bg-hero"
        style={{
          backgroundImage: `
          linear-gradient(
            180deg,
            #ffffff 0%,
            rgba(255, 255, 255, 0.8) 72.22%,
            rgba(250, 245, 245, 0.1) 148.9%
          ),
          url(${backgroundUrl})
        `,

          backgroundPosition: "top",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="flex flex-row items-center justify-center h-[40vh] max-md:h-[25vh]  w-full col-span-2">
          <div className="max-md:pt-0 col-span-2 text-center">
            <p className="heroText">Contact Us</p>
          </div>
        </div>
        <div className="flex items-center justify-center max-md:mt-0 max-md:pt-12">
          {/* Optional Image Section */}
        </div>
      </div>
      <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow mt-20 max-md:mt-14">
        <ContactForm />
      </div>
    </>
  );
};

export default ContactHero;
