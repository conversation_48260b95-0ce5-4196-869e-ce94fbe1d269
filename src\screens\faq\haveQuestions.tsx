import { Accordion, AccordionItem } from "@nextui-org/react";

const data = [
  {
    title: "General Information",
    item: [
      {
        heading: "What is a PMS (Portfolio Management Service)?",
        list: [
          "↳ PMS is an investment management service tailored for high-net-worth individuals (HNIs). It provides customized solutions for their financial investment needs through discretionary, non-discretionary, or advisory portfolios.",
        ],
      },
      {
        heading: "What is ArthAlpha’s investment philosophy?",
        list: [
          "↳ ArthAlpha’s investment strategy is driven by the MEQ (ML Equity Quant) approach, combining fundamental analysis, quantitative finance, data science, and behavioral psychology. Our focus is on delivering consistent and superior returns.",
        ],
      },
      {
        heading:
          "What is the minimum investment required to open a PMS account with ArthAlpha?",
        list: ["↳ The minimum ticket size for investment is ₹1 crore."],
      },
    ],
  },
  {
    title: "Portfolio Management",
    item: [
      {
        heading: "How does ArthAlpha select companies for its portfolio?",
        list: [
          "↳ We use advanced AI/ML algorithms alongside quantitative analysis to identify companies with strong growth potential, financial stability, and competitive advantages. Our focus is on low- and mid-frequency strategies to maximize returns.",
        ],
      },
      {
        heading: "What is the typical churn rate of the ArthAlpha portfolio?",
        list: [
          "↳ A higher churn rate reflects our proactive approach to adapting to changing market dynamics. By swiftly reallocating investments, we minimize impact costs and ensure our portfolio stays aligned with emerging opportunities, enhancing overall returns and risk management.",
        ],
      },
      {
        heading: "How does ArthAlpha handle portfolio diversification?",
        list: [
          "↳ While our portfolios are focused, they are strategically diversified across sectors and asset classes to optimize risk-adjusted returns.",
        ],
      },
    ],
  },
  {
    title: "Fees and Charges",
    item: [
      {
        heading: "What are the fees applicable for ArthAlpha PMS?",
        list: [
          "↳ Our fee structure includes a management and a performance-based fee. Management fees stand at 1.5% per annum, charged on a monthly basis. Our performance fee structure includes a 15% fee on returns exceeding a 10% hurdle rate, with a full catch-up mechanism to ensure we are rewarded only after clients achieve their agreed minimum return.",
        ],
      },
      {
        heading: "Are there any other charges besides management fees?",
        list: [
          "↳ Yes, charges include transaction fees, audit fees, and statutory levies like Securities Transaction Tax (STT) and GST.",
        ],
      },
      {
        heading: "What does the term 'high watermark' mean in fee calculation?",
        list: [
          "↳ The high watermark ensures performance fees are charged only when portfolio gains exceed the previous highest value after fees.",
        ],
      },
    ],
  },
  {
    title: "Account Management",
    item: [
      {
        heading:
          "Do investors need to open a new demat account for ArthAlpha PMS?",
        list: [
          "↳ Yes, a dedicated demat account in the investor's name is required to manage their investments efficiently.",
        ],
      },
      {
        heading: "How can investors monitor their portfolio performance?",
        list: [
          "↳ Investors receive monthly reports and gain access to our secure online portal, offering real-time updates on portfolio performance.",
        ],
      },
      {
        heading: "Can dividends from the portfolio be reinvested?",
        list: [
          "↳ Yes, dividends are credited to the client’s PMS account, and the amount after TDS is reinvested to enhance returns.",
        ],
      },
    ],
  },
  {
    title: "Investment Process",
    item: [
      {
        heading: "When does ArthAlpha decide to buy/sell a stock?",
        list: [
          "↳ Stocks are sold at the market's opening and typically bought in the afternoon after 2 PM. For large orders, we employ a TWAP (Time-Weighted Average Price) strategy, while smaller orders are executed as single trades.",
        ],
      },
      {
        heading: "Does ArthAlpha invest in small-cap companies?",
        list: [
          "↳ Our primary focus is on companies within the Nifty 200 universe. These are large-cap companies.",
        ],
      },
    ],
  },
  {
    title: "Taxation and Compliance",
    item: [
      {
        heading: "What is the tax treatment for PMS investments?",
        list: [
          "↳ The tax liability for PMS investments is similar to direct equity investments. We provide audited statements to assist clients with tax filings.",
        ],
      },
      {
        heading: "Is there a contract between ArthAlpha and its clients?",
        list: [
          "↳ Yes, a formal agreement outlines the terms and conditions of portfolio management, ensuring transparency and compliance with SEBI regulations.",
        ],
      },
    ],
  },
  {
    title: "Unique Features",
    item: [
      {
        heading: "What makes ArthAlpha different from other PMS providers?",
        list: [
          "↳ Our USP lies in leveraging cutting-edge quant-driven AI/ML strategies to provide bespoke investment solutions, focusing on behavioral finance and long-term value creation.",
        ],
      },
      {
        heading: "How does ArthAlpha’s AI/ML-driven approach benefit clients?",
        list: [
          "↳ It ensures data-driven decision-making, minimizes human biases, and identifies unique investment opportunities in dynamic markets.",
        ],
      },
    ],
  },
];

export default function FaqsAccordion() {
  return (
    <>
      <div>
        <p className="text-4xl font-bold max-md:text-2xl mt-20 mb-8 max-md:mt-12 text-center">
          Have questions? We've got you!
        </p>
      </div>
      <div>
        {data.map((section, index) => {
          return (
            <div key={index}>
              <p className="text-start text-xl max-md:text-base font-[600] mt-12 mb-3 max-md:mb-0 max-md:mt-8 uppercase ml-2">
                {section.title}
              </p>
              <Accordion variant="splitted" className="mb-0">
                {section.item.map((item, idx) => (
                  <AccordionItem
                    key={idx}
                    aria-label={item.heading}
                    title={
                      <p className="font-semibold pr-12 max-md:pr-6 py-6 md:px-1 max-md:text-sm max-md:py-3">
                        {item.heading}
                      </p>
                    }
                    indicator={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src="https://res.cloudinary.com/damm9iwho/image/upload/v1731050216/plus_dia0bt.svg"
                          alt=""
                          className="rotate-45"
                        />
                      ) : (
                        <img
                          src="https://res.cloudinary.com/damm9iwho/image/upload/v1731050216/plus_dia0bt.svg"
                          alt=""
                          color="red"
                        />
                      )
                    }
                    className="shadow-none border-2 rounded-[1.25rem] mt-3 max-md:mt-2 items-center hover:bg-[#f2f1f1]"
                  >
                    <div className="md:px-1">
                      {item.list.map((listItem, listIndex) => (
                        <p key={listIndex} className="mb-3">
                          {listItem}
                        </p>
                      ))}
                    </div>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          );
        })}
      </div>
    </>
  );
}
