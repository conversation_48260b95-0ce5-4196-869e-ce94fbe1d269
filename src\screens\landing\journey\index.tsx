import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, MoveRight } from "lucide-react";
import Link from "next/link";

const Journey = () => {
  return (
    <>
      <div className="mt-16 max-md:12 w-full">
        <Card
          className="p-0 bg-[#FAF5F5] border-none shadow-none"
          style={{ padding: 0 }}
        >
          <CardContent
          // className="flex flex-col justify-center items-center "
          //   style={{ padding: 0 }}
          >
            <div className="row justify-between items-center py-6 max-md:py-0 max-md:flex-col max-md:justify-start max-md:items-start">
              <div>
                <p className=" pt-6 text-5xl font-bold mb-3 max-md:text-3xl ">
                  Start your journey with
                </p>
                <img
                  src="/assets/artha.svg"
                  alt=""
                  className="max-md:w-[120px]"
                />
              </div>
              <div className="row justify-center mt-6">
                <Link
                  href="https://eclientreporting.nuvamaassetservices.com/wealthspectrum/app/loginWith"
                  target="_blank"
                >
                  <Button className="bg-primary text-white rounded-xl h-11 font-[600]">
                    {/* Get Started */}
                    Investor Login
                    <ArrowRight size={32} strokeWidth="3px" />
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default Journey;
