import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const MissionVision = () => {
  return (
    <>
      <div className="my-20 max-md:mt-16">
        <div className=" grid grid-cols-2 max-lg:grid-cols-2 max-md:grid-cols-1 gap-8 ">
          <Card className="missionVisionCard" style={{ padding: 0 }}>
            <CardContent
              className="flex flex-col justify-center items-center pt-6 px-10 max-md:px-4"
              //   style={{ padding: 0 }}
            >
              <p className="text-2xl max-md:text-xl font-bold">Our Vision</p>
              <p className="text-center pt-4 text-lg max-md:text-base text-secondary font-[500]  leading-[26px] md:px-12">
                To consistently deliver superior risk-adjusted returns through
                data-driven strategies and unwavering commitment to our
                investors’ success.
              </p>
            </CardContent>
          </Card>
          <Card className="missionVisionCard">
            <CardContent
              className="flex flex-col justify-center items-center pt-6 px-10 max-md:px-4"
              //   style={{ padding: 0 }}
            >
              <p className="text-2xl max-md:text-xl font-bold">Our Mission</p>
              <p className="text-center pt-4 text-lg max-md:text-base text-secondary font-[500] leading-[26px] md:px-12">
                To empower investors by leveraging advanced technology combined
                with fundamental insights and market experience.
              </p>
            </CardContent>
          </Card>
        </div>{" "}
      </div>
    </>
  );
};

export default MissionVision;
