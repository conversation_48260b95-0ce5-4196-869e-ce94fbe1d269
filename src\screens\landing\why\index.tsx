import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

const LandingWhy = () => {
  return (
    <>
      <div>
        <div className="row justify-center mb-6 max-md:mb-4  items-center gap-2 mt-12">
          <p className="heading mb-4">Why</p>
          <img
            src="/assets/artha.svg"
            alt=""
            className="h-[2rem] -mt-4 max-md:h-[1.5rem] max-md:-mt-5"
          />
        </div>
        <div className=" grid grid-cols-3 max-lg:grid-cols-2 max-md:grid-cols-1 gap-4 ">
          <Card
            className="p-0 shadow-none border-2 border-[#F1F3FA]"
            style={{ padding: 0 }}
          >
            <CardContent
              className="flex flex-col justify-center items-center "
              //   style={{ padding: 0 }}
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733724458/arthalpha/why1_dlocwd.svg"
                alt=""
                className="w-20 h-20 pt-6"
              />
              <p className="text-center pt-3 text-lg text-[#575757] font-bold md:px-8 max-md:text-base max-md:px-4">
                Data-Driven Strategies for Consistent Growth{" "}
              </p>
            </CardContent>
          </Card>
          <Card className="p-0 shadow-none border-2 border-[#F1F3FA]">
            <CardContent className="flex flex-col justify-center items-center ">
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734083141/arthalpha/img1_buwqkn.svg"
                alt=""
                className="w-20 h-20 pt-6 fill-primary"
              />
              <p className="text-center pt-3 text-lg text-[#575757] font-bold md:px-8 max-md:text-base max-md:px-4">
                AI & Quantitative Models to Eliminate Human Bias{" "}
              </p>
            </CardContent>
          </Card>
          <Card className="p-0 shadow-none border-2 border-[#F1F3FA]">
            <CardContent className="flex flex-col justify-center items-center ">
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734083141/arthalpha/img2_mgqn0c.svg"
                alt=""
                className="w-20 h-20 pt-6"
              />
              <p className="text-center pt-3 text-lg text-[#575757] font-bold md:px-8 max-md:text-base max-md:px-4">
                Empowering Clients with Transparent Solutions{" "}
              </p>
            </CardContent>
          </Card>
        </div>
        <div className="row justify-center mt-6 max-md:mt-2">
          <Link href="/investmentPhilosophy">
            <Button
              className="btn-border hover:bg-primary hover:text-white  mt-3"
              variant="outline"
            >
              Learn More
            </Button>
          </Link>
        </div>
      </div>
    </>
  );
};

export default LandingWhy;
