"use client";
import React, { useEffect, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5map from "@amcharts/amcharts5/map";
import am5geodata_worldLow from "@amcharts/amcharts5-geodata/worldLow"; // World geoJSON
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { DateTime } from "luxon";

const points = [
  {
    id: "point1",
    left: "14%",
    top: "30%",
    label: "USA",
    time: DateTime.now()
      .setZone("America/New_York")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/usa.svg",
    data: [
      {
        key: "^GSPC",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734672908/arthalpha/Frame_223532_lmewid.svg",
        title: "S&P 500",
        currencyIcon: "USD",
        value: "5,872.17",
        change: "−2.95%",
        changeStatus: "loss",
      },
      {
        key: "^DJI",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734673013/arthalpha/Frame_2235334_w7nzfc.svg",
        title: "Dow Jones Industrial",
        currencyIcon: "USD",
        value: "42,326.88",
        change: "−2.58%",
        changeStatus: "loss",
      },
      {
        key: "^BSESN",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734629797/arthalpha/Frame_22353_s4gfzd.svg",
        title: "US Composite",
        currencyIcon: "USD",
        value: "19,392.69",
        change: "−3.56%",
        changeStatus: "loss",
      },
    ],
  }, // USA
  {
    id: "point2",
    left: "48%",
    top: "15%",
    label: "UK",
    time: DateTime.now()
      .setZone("Europe/London")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/uk.svg",
    data: [
      {
        key: "^FTSE",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675683/arthalpha/Frame_223531_eibyl1.svg",
        title: "FTSE 100 ",
        currencyIcon: "GBP",
        value: "8,084.29",
        change: "−1.40%",
        changeStatus: "loss",
      },
      {
        key: "^FTAS",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223533_nwnzwg.svg",
        title: "FTSE All Share",
        currencyIcon: "GBP",
        value: "4,417.36",
        change: "−1.38%",
        changeStatus: "loss",
      },
      // {
      //   key: "^FTMC",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223534_u8eyaq.svg",
      //   title: "FTSE 250",
      //   currencyIcon: "GBP",
      //   value: "21,285.83",
      //   change: "−1.17%",
      //   changeStatus: "loss",
      // },
    ],
  }, // UK
  {
    id: "point3",
    left: "70%",
    top: "42%",
    label: "India",
    time: DateTime.now()
      .setZone("Asia/Kolkata")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/india.svg",
    data: [
      {
        key: "^NSEI",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675681/arthalpha/Frame_22353_2x456_wu704g.svg",
        title: "NIFTY 50",
        currencyIcon: "INR",
        value: "23,951.70",
        change: "−1.02%",
        changeStatus: "loss",
      },
      {
        key: "^BSESN",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/83264_q8ald3.svg",
        title: "SENSEX",
        currencyIcon: "INR",
        value: "79,218.05",
        change: "−1.20%",
        changeStatus: "loss",
      },
      // {
      //   key: "MONIFTY500.NS",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/74_wkklj8.svg",
      //   title: "NIFTY 500",
      //   currencyIcon: "INR",
      //   value: "22,752.15",
      //   change: "−0.79%",
      //   changeStatus: "loss",
      // },
    ],
  }, // India
  {
    id: "point4",
    left: "80%",
    top: "35%",
    label: "China",
    time: DateTime.now()
      .setZone("Asia/Shanghai")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/china.svg",
    data: [
      {
        key: "000001.SS",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675681/arthalpha/Frame_2235321634_mgxszx.svg",
        title: "SSE Composite",
        currencyIcon: "CNY",
        value: "3,370.0331",
        change: "−0.36%",
        changeStatus: "loss",
      },
      {
        key: "399001.SZ",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/Frame_22353273_bo4zss.svg",
        title: "Shenzhen",
        currencyIcon: "CNY",
        value: "10,649.0339",
        change: "+0.61%",
        changeStatus: "profit",
      },
      // {
      //   key: "399106.SZ",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/Frame_22353765_uwtjly.svg",
      //   title: "SZSE Composite",
      //   currencyIcon: "CNY",
      //   value: "2,032.8495",
      //   change: "+0.36%",
      //   changeStatus: "profit",
      // },
    ],
  }, //China
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Japan",
    time: DateTime.now()
      .setZone("Asia/Tokyo")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/japan.svg",
    data: [
      {
        key: "^BSESN",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223535_zxzkkf.svg",
        title: "Japan 225", //not on yahooo
        currencyIcon: "JPY",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "0P00006NXB.T",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
        title: "TOPIX",
        currencyIcon: "JPY",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "2552.T",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
      //   title: "Tokyo Stock Exchange",
      //   currencyIcon: "JPY",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // Japan
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "South Africa",
    time: DateTime.now()
      .setZone("Africa/Johannesburg")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618986/arthalpha/south_africa_udlk54.svg",
    data: [
      {
        key: "WIZAF.FGI",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618984/arthalpha/er_wobrej.svg",
        title: "FTSE South Africa", //not on yahooo
        currencyIcon: "ZAR",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      // {
      //   key: "0P00006NXB.T",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
      //   title: "TOPIX",
      //   currencyIcon: "¥",
      //   value: "2,713.83",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
      // {
      //   key: "2552.T",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
      //   title: "Tokyo Stock Exchange",
      //   currencyIcon: "¥",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // South Africa
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "korea",
    time: DateTime.now()
      .setZone("Asia/Seoul")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/south_korea_nlwqaf.svg",
    data: [
      {
        key: "^KS11",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618986/arthalpha/kospi_xbmlyq.svg",
        title: "KOSPI Composite", //not on yahooo
        currencyIcon: "KRW",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "100910.KS",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/south_korea_nlwqaf.svg",
        title: "KRX 100",
        currencyIcon: "KRW",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "KOSPI50EW.KS",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618986/arthalpha/kospi500_ehlu7x.svg",
      //   title: "KOSPI 50",
      //   currencyIcon: "KRW",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // korea
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Dubai",
    time: DateTime.now()
      .setZone("Asia/Dubai")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/japan.svg",
    data: [
      {
        key: "^BSESN",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223535_zxzkkf.svg",
        title: "Japan 225", //not on yahooo
        currencyIcon: "¥",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "0P00006NXB.T",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
        title: "TOPIX",
        currencyIcon: "¥",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "2552.T",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
      //   title: "Tokyo Stock Exchange",
      //   currencyIcon: "¥",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // Dubai
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Canada",
    time: DateTime.now()
      .setZone("America/Toronto")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "/assets/flag/japan.svg",
    data: [
      {
        key: "^BSESN",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223535_zxzkkf.svg",
        title: "Japan 225", //not on yahooo
        currencyIcon: "CAD",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "0P00006NXB.T",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
        title: "TOPIX",
        currencyIcon: "CAD",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "2552.T",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
      //   title: "Tokyo Stock Exchange",
      //   currencyIcon: "CAD",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // Canada
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "UAE",
    time: DateTime.now()
      .setZone("Asia/Dubai")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618984/arthalpha/uae_g6rcau.svg",
    data: [
      {
        key: "DFMGI.AE",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618984/arthalpha/dfm_ut1dft.svg",
        title: "DFM General", //not on yahooo
        currencyIcon: "AED",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "FADGI.FGI",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618984/arthalpha/adx_dahd2b.svg",
        title: "FTSE ADX General",
        currencyIcon: "AED",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "FADX15.FGI",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618984/arthalpha/15_ihqufe.svg",
      //   title: "FTSE ADX 15",
      //   currencyIcon: "AED",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // uae
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Australia",
    time: DateTime.now()
      .setZone("Australia/Sydney")
      .toFormat("dd LLL yyyy hh:mm:ss a"),
    flag: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618983/arthalpha/australia_jys4lq.svg",
    data: [
      {
        key: "^AXJO",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/snp200_kagqdv.svg",
        title: "S&P/ASX 200", //not on yahooo
        currencyIcon: "AUD",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        key: "QOZ.AX",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618983/arthalpha/australia_jys4lq.svg",
        title: "Australia 200",
        currencyIcon: "AUD",
        value: "2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      // {
      //   key: "^CLTBOMEGA",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/all_index_mbfqqe.svg",
      //   title: "All Ordinaries Index",
      //   currencyIcon: "AUD",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // Australia
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Brazil",
    time: DateTime.now()
      .setZone("America/Sao_Paulo")
      .toFormat("dd LLL yyyy hh:mm a"),
    flag: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/brazil_bzj0ik.svg",
    data: [
      {
        key: "FRBR5.FGI",
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737618982/arthalpha/50_pwibn3.svg",
        title: "Brazil 50", //not on yahooo
        currencyIcon: "AUD",
        value: "38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      // {
      //   key: "QOZ.AX",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
      //   title: "Australia 200",
      //   currencyIcon: "AUD",
      //   value: "2,713.83",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
      // {
      //   key: "^CLTBOMEGA",
      //   icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
      //   title: "All Ordinaries Index",
      //   currencyIcon: "AUD",
      //   value: "1,396.67",
      //   change: "−0.22%",
      //   changeStatus: "loss",
      // },
    ],
  }, // Brazil
];

async function getCompanyDataOnly(symbols: string[]) {
  try {
    const response = await fetch(
      `/api/getCompanyData?symbols=${symbols.join(",")}`
    );
    const data = await response.json();

    if (data && data.length > 0) {
      // console.log(data[0].regularMarketPrice); // Log the price
      // console.log(data[0].regularMarketChangePercent);
      // return data[0].regularMarketPrice, data[0].regularMarketChangePercent;

      return data[0];
    }
  } catch (error) {
    console.error("Error fetching Yahoo Finance data:", error);
  }
}
const AmChart = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const intervalId = setInterval(() => {
      // getCompanyDataOnly(); // Call your function here
      points.map((item, index) => {
        item.data.map(async (dataItem, dataIndex) => {
          const data = await getCompanyDataOnly([dataItem.key]);
          // console.log(data);
          dataItem.value = data.regularMarketPrice;
          dataItem.change = data.regularMarketChangePercent.toFixed(2);
          dataItem.changeStatus =
            data.regularMarketChangePercent > 0 ? "profit" : "loss";
        });
      });
    }, 5000); // 2 seconds interval

    if (chartRef.current) {
      // Create root and chart
      // const root = am5.Root.new(chartRef.current);
      var root = am5.Root.new(chartRef.current, {
        tooltipContainerBounds: {
          top: 200,
          right: 0,
          bottom: 0,
          left: 0,
        },
      });

      // Remove the watermark/logo
      root?._logo?.dispose(); // This removes the watermark/logo

      // Set themes
      root.setThemes([new am5themes_Animated(root, true)]);

      const chart = root.container.children.push(
        am5map.MapChart.new(root, {
          panX: "none", // Disable X-axis rotation
          panY: "none", // Disable Y-axis rotation
          projection: am5map.geoNaturalEarth1(),
          wheelX: "none",
          wheelY: "none",
          pinchZoom: false,
          // wheelZoom: false, // Disable zoom on scroll
          // zoomControl: am5map.ZoomControl.new(root, { disabled: true }), // Disable zoom control
          // wheelZoom: false,
          // tooltip: am5.Tooltip.new(root, {
          //   keepTargetHover: true,
          //   interactive: true,
          // }),
          // tooltip: am5.Tooltip.new(root, {
          //   getFillFromSprite: false,
          //   keepTargetHover: true,
          //   interactive: true,
          //   background: am5.Rectangle.new(root, {
          //     fill: am5.color(0xffffff),
          //     fillOpacity: 1,
          //     stroke: am5.color(0x000000),
          //   }),
          // }),
        })
      );

      var polygonSeries = chart.series.push(
        am5map.MapPolygonSeries.new(root, {
          geoJSON: am5geodata_worldLow, // Use global geoJSON
          exclude: ["AQ"], // Exclude Antarctica
        })
      );

      polygonSeries.mapPolygons.template.setAll({
        interactive: true,
        templateField: "settings",
        fill: am5.color(0xe7e7e7), // Lighter red on hover
      });

      // Set hover state color to lighter red only for the specified countries
      const highlightCountries = ["IN", "CN", "GB", "US", "JP"]; // List of countries to highlight

      interface CountryData {
        id: string;
        settings?: {
          fill?: am5.Color;
          tooltipText?: string;
        };
      }

      // polygonSeries.mapPolygons.template.events.on("pointerover", (ev) => {
      //   const polygon = ev.target;
      //   const dataContext = polygon?.dataItem?.dataContext as CountryData; // Cast to the CountryData type
      //   const countryId = dataContext?.id; // Safely retrieve the country ID
      //   const highlightCountries = ["IN", "CN", "GB", "US", "JP"]; // List of countries to highlight

      //   // Apply hover effect only if the country is in the list
      //   if (highlightCountries.includes(countryId)) {
      //     polygon.states.create("hover", {
      //       fill: am5.color(0xffcccc), // Lighter red on hover
      //       fillPattern: am5.LinePattern.new(root, {
      //         color: am5.color(0xffffff), // Line pattern color
      //         rotation: 45, // Pattern rotation
      //         strokeWidth: 1, // Pattern stroke width
      //       }),

      //       // tooltipHTML:
      //       //   '<div className="flex flex-row items-center justify-center"><b>{name}</b><br>Test<br><a href="https://en.wikipedia.org/" target="_blank">Link Text</a><br/></div>',
      //     });
      //     // polygon.set("state", "hover");
      //     // Set the tooltip HTML
      //     polygon.set(
      //       "tooltipHTML",
      //       `
      //       <div style="minWidth:'400px'">
      //       <div style="display: flex; flex-direction: row; align-items: center; justify-content: space-between; text-align: center;">
      //       <div
      //       style="display: flex; flex-direction: row; align-items: center; justify-content: center; text-align: center;"
      //       >
      //       <img
      //           src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734629797/arthalpha/Frame_22353_s4gfzd.svg"

      //                 alt=""
      //                 className="w-8 h-8"
      //               />
      //   <strong>NIFTY 50{name=="India"?"Danish":"z"}</strong>
      //   </div>
      //   <div
      //   style="display: flex; flex-direction: row; align-items: center; justify-content: center; text-align: center;"
      //   >
      //   <p>₹23,951.70</p>
      //   <p>(−1.02%)</p>
      //   </div>

      // </div>

      // `
      //     );
      //   }
      // });

      polygonSeries.mapPolygons.template.events.on("pointerover", (ev) => {
        const polygon = ev.target;
        const dataContext = polygon?.dataItem?.dataContext as CountryData; // Cast to the CountryData type
        const countryId = dataContext?.id; // Safely retrieve the country ID
        const countryMapping: { [key: string]: string } = {
          IN: "India",
          CN: "China",
          GB: "UK",
          US: "USA",
          JP: "Japan",
          ZA: "South Africa",
          KR: "korea",
          // AE: "Dubai",
          CA: "Canada",
          AE: "UAE",
          AU: "Australia",
          BR: "Brazil",
        }; // Map country codes to their names

        const countryName = countryMapping[countryId];

        if (countryName) {
          // Find the matching data point from the `points` array
          const pointData = points.find((point) => point.label === countryName);

          if (pointData) {
            // Set the tooltip dynamically with the matched data
            polygon.set(
              "tooltipHTML",
              `
              <div 
              class="min-w-[300px] max-md:min-w-[150px]"
              style=" font-family: Arial, sans-serif; border-radius:12px; background: white; color: black; ">
              <div class="flex flex-row items-center justify-between gap-3 px-1 py-1 rounded-lg bg-[#FAF5F5] border-bottom-width: 1px border-red-900">
                <div class="flex flex-row items-center gap-2 flex-nowrap">
                  <img src="${
                    pointData.flag
                  }"   class="h-[20px] w-[20px] max-md:h-[16px] max-md:w-[16px]" />
                  <div class="text-[12px] max-md:text-[10px] font-semibold">${
                    pointData.label
                  }</div>
                </div>
                <small class="text-[#929292] text-[10px] max-md:text-[8px]">${
                  pointData.time
                }</small>
                </div>
                <div>
                  ${pointData.data
                    .map(
                      (item) => `
                    <div style="display: flex; align-items: center; margin-top: 8px; border-top-width: 1px;
  border-color: #e2e2e2;padding-bottom:2px,padding-top:10px;flex-direction:row;justify-content:space-between;padding-top:6px ;gap:12px">
                    
                    <div style="display: flex; align-items: center;flex-direction:row; padding-top:6px padding-right:4px">
                      <img src="${item.icon}" alt="${
                        item.title
                      }" style="margin-right: 8px;" class="h-[24px] w-[24px] max-md:h-[18px] max-md:w-[18px]" />


                       <div style="font-weight: 600;" class="text-[12px] max-md:text-[10px]">${
                         item.title
                       }</div>
                      </div>
                      <div style="display: flex; align-items: center;flex-direction:row;justify-content:end;">
                       
                        <div  class="text-[12px] max-md:text-[10px]">
                         <span style="font-weight: 400; text-[10px] max-md:text-[10px]">${
                           item.currencyIcon
                         }</span>
                          <span style="font-weight: 500;">${item.value}</span>
                          <span style="color: ${
                            item.changeStatus === "loss" ? "red" : "green"
                          };">${item.change}</span>
                        </div>
                      </div>
                    </div>
                  `
                    )
                    .join("")}
                </div>
              </div>
              `
            );
            polygon.set(
              "tooltip",
              am5.Tooltip.new(root, {
                getFillFromSprite: false,
                getStrokeFromSprite: false,
                keepTargetHover: true,
                interactive: true,
                background: am5.RoundedRectangle.new(root, {
                  fill: am5.color(0xffffff),
                  fillOpacity: 1,
                  strokeWidth: 0.2,
                  stroke: am5.color(0x000000),
                }),
              })
            );
            // polygon.set("tooltipPosition", "pointer");

            // polygon.set("showTooltipOn", "always");
          }
        }
      });

      polygonSeries.mapPolygons.template.events.on("pointerout", (ev) => {
        const polygon = ev.target;
        // polygon.set("state", "default"); // Reset to default state
      });

      // Filter the data to only include the specified countries and set color
      polygonSeries.data.setAll([
        {
          id: "IN", // India
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "India", // Tooltip text for India
          },
        },
        {
          id: "CN", // China
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "China", // Tooltip text for China
          },
        },
        {
          id: "JP", // Japan
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "Japan", // Tooltip text for Japan
          },
        },
        {
          id: "GB", // United Kingdom
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "United Kingdom", // Tooltip text for UK
          },
        },
        {
          id: "US", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "United States", // Tooltip text for US
          },
        },
        {
          id: "ZA", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "South Africa", // Tooltip text for US
          },
        },
        {
          id: "KR", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "South korea ", // Tooltip text for US
          },
        },
        // {
        //   id: "AE", // United States
        //   settings: {
        //     fill: am5.color(0x800020), // Solid red color
        //     tooltipText: "Dubai", // Tooltip text for US
        //   },
        // },
        {
          id: "AU", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "Australia", // Tooltip text for US
          },
        },
        // {
        //   id: "CA", // United States
        //   settings: {
        //     fill: am5.color(0x800020), // Solid red color
        //     tooltipText: "Canada", // Tooltip text for US
        //   },
        // },
        {
          id: "BR", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "Brazil", // Tooltip text for US
          },
        },
        {
          id: "AE", // United States
          settings: {
            fill: am5.color(0x800020), // Solid red color
            tooltipText: "UAE", // Tooltip text for US
          },
        },
      ]);

      return () => {
        root.dispose(); // Dispose chart on component unmount
      };
    }
  }, [getCompanyDataOnly]);

  return (
    <div
      id="chartdiv"
      ref={chartRef}
      // style={{ width: "100%", height: "300px" }}
      className="w-full h-[500px] max-md:h-[200px] max-md:-mt-24"
    />
  );
};

export default AmChart;
