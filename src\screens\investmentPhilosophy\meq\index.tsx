import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

const data = [
  {
    icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1735192998/arthalpha/Frame_2018775059_bilcbx.svg",
    title:
      "Built on our four core pillars, ArthAlpha’s MEQ (ML Equity Quant) Strategy leverages machine learning and financial expertise for a dynamic investing approach.",
  },
  {
    icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1735192998/arthalpha/kh_baeb8j.svg",
    title:
      "By leveraging cutting-edge technology and real-time data, we aim to consistently deliver alpha and outperform market benchmarks.",
  },
  {
    icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1735192998/arthalpha/Frame_2018775058_bniv5f.svg",
    title:
      "This strategy is designed not only to optimize returns but to manage risk effectively, ensuring that our investors achieve sustainable, long-term growth.",
  },
];
const MEQ = () => {
  return (
    <>
      <div className=" container mx-auto px-32 max-lg:12 max-md:px-4 mb-32 mt-12 max-md:mt-6">
        <p className="text-center text-4xl max-md:text-2xl font-bold">
          ML Equity Quant Strategy (MEQ)
        </p>

        <Card
          //   key={index}
          className={`rounded-[32px] max-md:w-[100%] bg-[#e9e9e9] card-item max-md:rounded-[30px] shadow-none border-1 border-[#0000000f] mt-12 max-md:mt-6`}
        >
          <CardContent className="p-3 max-md:p-2 max-lg:p-4 grid grid-cols-3 gap-4 max-md:gap-2 max-md:grid-cols-1 max-lg:grid-cols-2 max-xl:grid-cols-3">
            {data.map((item, index) => (
              <div className="w-full p-8 bg-white rounded-[24px] max-md:p-4 box-shadow">
                {/* <div className="grid grid-cols-3 gap-4 max-md:gap-2 max-md:grid-cols-1"> */}
                <div className="flex flex-col gap-3 items-start justify-between max-md:justify-start  ">
                  <div className="">
                    <img
                      src={
                        item.icon
                          ? item.icon
                          : "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733773535/arthalpha/img2_m2my8m.svg"
                      }
                      // sizes="40px"
                      alt="behance Logo"
                      className="w-[60px] h-[60px] min-w-[70px] min-h-[70px]  mb-3 max-xl:min-w-[40px] max-xl:min-h-[40px] "
                    />

                    <p className="text-xl max-md:text-base  max-xl:text-sm font-[600]  mt-4">
                      {item.title}
                    </p>
                  </div>
                  <div>
                    {/* <p className="text-[#A2A2A2] text-lg font-bold">Objective</p>
                  <p className="text-lg  font-[500]">{item.objective}</p> */}
                  </div>
                </div>
                {/* </div> */}
              </div>
            ))}
          </CardContent>
        </Card>
        <div className="row justify-center mt-7 max-md:mt-4">
          <Link href="/MLEquity">
            <Button
              className="btn-border hover:bg-primary hover:text-white  mt-4"
              variant="outline"
            >
              Learn More
            </Button>
          </Link>
        </div>
      </div>
    </>
  );
};

export default MEQ;
