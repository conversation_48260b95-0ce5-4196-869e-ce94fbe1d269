"use client";
import React, { useState } from "react";
import Step<PERSON><PERSON>ontent from "./stepperContent";

const initialStepsArr = [
  {
    stepLabel: "Shipping address",
    stepDescription: "Add your delivery address",
    completed: true,
  },
  {
    stepLabel: "Payment details",
    stepDescription: "Add your mode of payment",
    completed: false,
  },
  {
    stepLabel: "Review your order",
    stepDescription: "Verify your order details",
    completed: false,
  },
];

export default function StepperComp() {
  const [currentStep, setCurrentStep] = useState(0);

  const handleStepClick = (step: any, index: any) => setCurrentStep(index);

  return (
    <div className="mt-12 mb-32">
      <StepperContent />
    </div>
  );
}
