import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    if (!config.resolve) config.resolve = { alias: {} };
    config.externals = [...config.externals, "canvas", "jsdom"];
    config.resolve.alias = {
      ...config.resolve.alias,
      "pdfjs-dist": "pdfjs-dist/legacy/build/pdf", // Force Legacy Version
    };

    return config;
  },

  // Add security headers to prevent clickjacking
  async headers() {
    return [
      {
        // Apply to all routes
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY", // Prevents embedding in ANY iframe
          },
          {
            key: "Content-Security-Policy",
            value: "frame-ancestors 'none';", // Modern alternative to X-Frame-Options
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=(), payment=()",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
