import React, { useEffect, useState } from "react";
import { Tabs, Tab } from "@nextui-org/tabs";
import Complaints from "./complaints";
import Disclosure from "./disclosure";
import InvestmentCharter from "./investmentCharter";
import CodeofConduct from "./codeofConduct";
import InvestorCharter from "./investorCharter";

export default function GovernanceTabs() {
  const [isVertical, setIsVertical] = React.useState(true);

  useEffect(() => {
    // Function to check the viewport width and update state
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsVertical(false); // Set to false for mobile view
      } else {
        setIsVertical(true); // Set to true for web view
      }
    };

    // Check initially on component mount
    handleResize();

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return (
    <div className="pt-8 pb-32">
      <div className="mt-6 mb-8">
        <p className=" text-center font-bold text-4xl">Governance</p>
        {/* <p>Manage your account settings and set e-mail preferences.</p> */}
      </div>
      <div className="flex flex-col ">
        <Tabs
          aria-label="Options"
          isVertical={isVertical}
          className="bg-none text-xl "
          variant="light"
          radius="sm"
          classNames={{
            tabList: "border-[#F2F2F2] shadow-none",
            cursor: "bg-[#F2F2F2] rounded-[10px]",
            tab: "md:py-3 h-[45px] max-md:h-[30px] md:justify-start  border-[#F2F2F2] shadow-none md:px-4 max-md:px-2",
            tabContent:
              " group-data-[selected=true]:text-[#000] text-lg font-[500] text-[#B7B7B7] max-md:text-xs",
          }}
        >
          <Tab key="Complaints" title="Complaints" className="">
            {/* <Card className="md:ml-10  text-base shadow-none p-0 rounded-none">
              <CardBody className="p-0"> */}

            <div className="md:ml-10">
              <Complaints />
            </div>
            {/* </CardBody>
            </Card> */}
          </Tab>
          <Tab key="Investment Charter" title="Investment Charter" className="">
            <div className="md:ml-10">
              <InvestmentCharter />
            </div>
          </Tab>
          <Tab key="Disclosure" title="Disclosure" className="">
            <div className="md:ml-10">
              <Disclosure />
            </div>
          </Tab>
          <Tab key="Code of Conduct" title="Code of Conduct" className="">
            <div className="md:ml-10">
              <CodeofConduct></CodeofConduct>
            </div>
          </Tab>
          <Tab key="Investor Charter" title="Investor Charter" className="">
            <div className="md:ml-10">
              <InvestorCharter></InvestorCharter>
            </div>
          </Tab>
        </Tabs>
      </div>
    </div>
  );
}
