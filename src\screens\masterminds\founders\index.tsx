import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

const Founders = () => {
  return (
    <>
      <div>
        <h2 className="heading font-bold mb-12 max-md:mb-6 text-center">
          Founders
        </h2>
        <Card
          className="p-0 rounded-xl border-none bg-[#FAF5F5] "
          style={{ padding: 0 }}
        >
          <CardContent className="p-6 max-md:p-4 grid grid-cols-5 max-md:grid-cols-1 pt-6">
            <div className="col-span-3 max-md:col-span-1 order-1 max-md:order-2">
              <p className="text-2xl max-md:text-xl font-bold">Roh<PERSON><PERSON></p>
              <p className="text-lg max-md:text-base font-[500] pt-2">
                Ex-CIO of True Beacon, 20+ years experience
              </p>
              <p className="pt-3 text-lg max-md:text-base text-[#6B7280] font-[400] ">
                <PERSON><PERSON><PERSON> is a CA, a Stanford GSB graduate, and a Data Scientist
                with a stellar track record as an investment manager. He
                co-founded Roaring Numbers, a U.S. hedge fund, and Riemann
                Capital, a Singapore-based wealth advisory firm. His leadership
                and expertise have contributed to the success of True Beacon,
                ICICI Bank, Citibank, Crédit Agricole, and ANZ
              </p>
              <Link
                href="https://www.linkedin.com/in/rohit-beri/"
                target="_blank"
              >
                <img
                  src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066311/arthalpha/Social_icon02_wcu9ul.svg"
                  alt=""
                  className="mt-3"
                />
              </Link>
              <Card className="bg-white mt-16 max-md:mt-12 ml-3 border-none rounded-xl shadow-none">
                <CardContent className="relative pt-6 p-6 max-md:p-4">
                  <img
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066311/arthalpha/Vector53_wn6dr7.svg"
                    alt=""
                    className=" absolute -top-4 -left-4"
                  />
                  <div className="row md:justify-between max-md:flex-col max-md:items-start">
                    <p className="text-primary text-2xl max-md:text-xl font-bold max-md:mb-6">
                      True wealth is built on a foundation of knowledge,
                      resilience, and integrity.
                    </p>
                    <img
                      src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739339683/arthalpha/Frame_1984078707_pnjrcb.svg"
                      alt=""
                      className="max-w-[200px] max-md:max-w-[150px]"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="md:-mt-20 max-md:-mb-16 col-span-2 flex flex-row justify-end order-2 max-md:order-1 ">
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066364/arthalpha/beri_f0thd3.svg"
                alt=""
                className="w-[90%] h-[70%]"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-32 max-md:mt-8">
        <Card
          className="p-0 rounded-xl border-none bg-[#FAF5F5] "
          style={{ padding: 0 }}
        >
          <CardContent className="p-6 max-md:p-4 grid grid-cols-5 max-md:grid-cols-1 pt-6">
            <div className="md:-mt-20  max-md:-mb-16  col-span-2 flex flex-row justify-start">
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066364/arthalpha/jha_nskkr6.svg"
                alt=""
                className="w-[90%] h-[70%]"
              />
            </div>
            <div className="col-span-3 max-md:col-span-1">
              <p className="text-2xl max-md:text-xl font-bold">
                Rohit Kumar Jha
              </p>
              <p className="text-lg max-md:text-base font-[500] pt-2">
                Ex-PM Tower Research Capital, 8+ years experience
              </p>
              <p className="pt-3 text-lg max-md:text-base text-[#6B7280] font-[400] ">
                Rohit, an IIT Kanpur CS graduate, transitioned from being a top
                hedge fund manager to an entrepreneur. He spearheaded a highly
                profitable trading unit at WorldQuant and became one of Tower
                Research Capital’s youngest portfolio managers. Today, he
                provides expert guidance on quantitative strategies and risk
                management
              </p>
              <Link
                href="https://www.linkedin.com/in/rohitkjha/"
                target="_blank"
              >
                <img
                  src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066311/arthalpha/Social_icon02_wcu9ul.svg"
                  alt=""
                  className="mt-3"
                />
              </Link>
              <Card className="bg-white mt-16 max-md:mt-12 ml-3 border-none rounded-xl shadow-none">
                <CardContent className="relative pt-6 p-6 max-md:p-4">
                  <img
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734066311/arthalpha/Vector53_wn6dr7.svg"
                    alt=""
                    className=" absolute -top-4 -left-4"
                  />
                  <div className="row md:justify-between max-md:flex-col max-md:items-start">
                    <p className="text-primary text-2xl max-md:text-xl font-bold max-md:mb-6">
                      Innovation and discipline are the cornerstones of success.
                    </p>
                    <img
                      src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739339683/arthalpha/rohit_jha_aigmpj.svg"
                      alt=""
                      className="max-w-[200px] max-md:max-w-[150px] h-[50px] max-h-[50px]"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default Founders;
