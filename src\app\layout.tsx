import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { NextUIProvider } from "@nextui-org/react";
import { Headder } from "@/components/navBar";
import Footer from "@/components/footer";
import TopBannerWrapper from "./TopBannerWrapper";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "ArthAlpha",
  description: "Human Ingenuity, Quantitative Excellence",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextUIProvider>
          <TopBannerWrapper />
          {children}
          <Footer />
        </NextUIProvider>
      </body>
    </html>
  );
}
