"use client";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import Link from "next/link";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dalBody,
  ModalFooter,
  useDisclosure,
} from "@nextui-org/modal";
import { useState } from "react";
import { Button } from "@/components/ui/button";

export default function MediaCardComp(props: any) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [item, setItem] = useState(1);
  const [selectedCard, setSelectedCard] = useState<any>(null);

  const handlePlus = () => {
    setItem(item + 1);
  };

  const handleMinus = () => {
    if (item > 1) setItem(item - 1);
  };

  // Function to open the modal with the selected card data
  const handleOpenModal = (cardData: any) => {
    setSelectedCard(cardData);
    onOpen();
  };

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItem, setSelectedItem]: any = useState();

  // Open modal and set selected item
  const handleReadMore = (item: any) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <>
      <Card className="p-0 rounded-xl border-[0.5px] border-[#E6E6E6] h-full">
        <CardContent
          className="relative rounded-2xl flex flex-col h-full "
          style={{ padding: 0 }}
        >
          <div className="relative flex-grow flex flex-col">
            <Link href={props.item.link} target="_blank">
              <img
                src={props.item.img}
                alt=""
                className="h-[200px] w-full object-cover rounded-tl-xl rounded-tr-xl"
              />
            </Link>

            <div className="absolute top-2 right-2">
              <Button variant="secondary" className="text-white blur-0 h-8">
                {props.mediaType === "All" ? props.item.type : props.mediaType}
              </Button>
            </div>

            <div className="p-5 max-md:p-4 -mb-6 flex-grow">
              <Link href={props.item.link} target="_blank">
                <p className="mt-1 text-xl font-bold">{props.item.heading}</p>
              </Link>
            </div>
          </div>

          <div className="p-5 max-md:p-4 mt-auto">
            <div className="bg-[#FAF5F5] rounded-xl p-3">
              <p className="line-clamp-3">{props.item.description}</p>
              <Button
                className="text-base font-bold text-primary mt-1 hover:text-primary hover:bg-primary  hover:bg-opacity-10 -ml-2 px-2 "
                onClick={() => handleReadMore(props.item)}
                variant="ghost"
              >
                Read More
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Modal
        isOpen={isModalOpen}
        placement="center"
        onClose={closeModal}
        size="lg"
        className="bg-white max-w-[60vw] max-lg:max-w-[50vw] max-md:max-w-[90vw]  rounded-xl py-1 custom-modal-backdrop max-md:px-4"
        backdrop="blur"
      >
        <ModalContent className="-mt-10 p-6 max-md:p-4">
          <ModalHeader className="font-bold text-xl">
            {selectedItem?.heading}
          </ModalHeader>
          <ModalBody className="max-h-[55vh] overflow-y-auto max-md:p-2">
            <img
              src={selectedItem?.img}
              alt=""
              className="w-full h-auto rounded"
            />
            <div className="bg-[#FAF5F5]  rounded-xl p-3">
              <p>{selectedItem?.description}</p>
              <p className="text-right mt-2">{selectedItem?.auther}</p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="primary" onClick={closeModal} className="text-white">
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
