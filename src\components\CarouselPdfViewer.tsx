"use client";

import React, { useState, useCallback, useEffect } from "react";
import { <PERSON>er, Worker } from "@react-pdf-viewer/core";
import { pageNavigationPlugin } from "@react-pdf-viewer/page-navigation";
import * as pdfjs from "pdfjs-dist";
import { ArrowLeft, ArrowRight, ZoomIn, ZoomOut, RotateCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Import styles
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/page-navigation/lib/styles/index.css";

// Ensure worker is set only in the browser (fixes SSR issue)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

interface CarouselPdfViewerProps {
  fileUrl: string;
  className?: string;
  showDots?: boolean;
  showZoomControls?: boolean;
  autoHeight?: boolean;
}

const CarouselPdfViewer: React.FC<CarouselPdfViewerProps> = ({
  fileUrl,
  className,
  showDots = true,
  showZoomControls = false,
  autoHeight = false,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [numPages, setNumPages] = useState(0);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);

  // Create page navigation plugin
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const { jumpToPage } = pageNavigationPluginInstance;

  const handleDocumentLoad = useCallback((e: any) => {
    setNumPages(e.doc.numPages);
    setCurrentPage(0);
  }, []);

  const handlePageChange = useCallback((e: any) => {
    setCurrentPage(e.currentPage);
  }, []);

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 0) {
      jumpToPage(currentPage - 1);
    }
  }, [currentPage, jumpToPage]);

  const goToNextPage = useCallback(() => {
    if (currentPage < numPages - 1) {
      jumpToPage(currentPage + 1);
    }
  }, [currentPage, numPages, jumpToPage]);

  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev + 0.2, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        goToPreviousPage();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        goToNextPage();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [goToPreviousPage, goToNextPage]);

  const canScrollPrev = currentPage > 0;
  const canScrollNext = currentPage < numPages - 1;

  return (
    <div className={cn("relative w-full bg-gray-50", className)}>
      {/* Main PDF Viewer Container */}
      <div className="relative overflow-hidden rounded-lg bg-white shadow-lg">
        <Worker
          workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
        >
          <div 
            className="pdf-viewer-container"
            style={{ 
              height: autoHeight ? "auto" : "70vh",
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              transformOrigin: "center center",
              transition: "transform 0.3s ease"
            }}
          >
            <Viewer
              fileUrl={fileUrl}
              plugins={[pageNavigationPluginInstance]}
              onDocumentLoad={handleDocumentLoad}
              onPageChange={handlePageChange}
            />
          </div>
        </Worker>

        {/* Navigation Buttons - styled like your existing carousel */}
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "absolute left-4 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white hover:bg-gray-200 text-primary shadow-lg z-20 transition-all duration-200",
            !canScrollPrev && "opacity-50 cursor-not-allowed"
          )}
          disabled={!canScrollPrev}
          onClick={goToPreviousPage}
        >
          <ArrowLeft className="h-5 w-5" />
          <span className="sr-only">Previous page</span>
        </Button>

        <Button
          variant="outline"
          size="icon"
          className={cn(
            "absolute right-4 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white hover:bg-gray-200 text-primary shadow-lg z-20 transition-all duration-200",
            !canScrollNext && "opacity-50 cursor-not-allowed"
          )}
          disabled={!canScrollNext}
          onClick={goToNextPage}
        >
          <ArrowRight className="h-5 w-5" />
          <span className="sr-only">Next page</span>
        </Button>

        {/* Page Counter */}
        {numPages > 0 && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/80 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
            {currentPage + 1} / {numPages}
          </div>
        )}

        {/* Zoom and Rotation Controls */}
        {showZoomControls && (
          <div className="absolute top-4 right-4 flex flex-col gap-2 z-20">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md"
              onClick={handleZoomIn}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md"
              onClick={handleZoomOut}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md"
              onClick={handleRotate}
            >
              <RotateCw className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Page Dots Indicator (like your existing carousel) */}
      {showDots && numPages > 0 && numPages <= 15 && (
        <div className="flex justify-center mt-6 space-x-2">
          {Array.from({ length: numPages }, (_, index) => (
            <button
              key={index}
              className={cn(
                "h-2 rounded-full transition-all duration-300 hover:bg-primary/70",
                index === currentPage
                  ? "bg-primary w-8"
                  : "bg-gray-300 w-2"
              )}
              onClick={() => jumpToPage(index)}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Page Numbers for many pages */}
      {numPages > 15 && (
        <div className="flex justify-center mt-6">
          <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-md">
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPreviousPage}
              disabled={!canScrollPrev}
              className="h-8 w-8 p-0"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[80px] text-center">
              {currentPage + 1} of {numPages}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={goToNextPage}
              disabled={!canScrollNext}
              className="h-8 w-8 p-0"
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CarouselPdfViewer;
