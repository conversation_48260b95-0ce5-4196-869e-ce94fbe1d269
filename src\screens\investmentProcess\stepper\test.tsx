import { useState } from "react";
import { cn } from "@/lib/utils"; // For Tailwind's class merging (you can replace this with a simple classNames function)

const steps = [
  {
    title: "Strategic Hypothesis Development",
    objective: "Craft hypotheses rooted in robust research.",
    points: [
      "Our journey begins by identifying alpha factors—unique insights capable of predicting future stock returns.",
      "Anchored in causal inference, our hypothesis formation avoids statistical coincidences and ensures signals are logically sound.",
      "This process sets the foundation for precision in investment decisions.",
    ],
  },
  {
    title: "Step 2 Title",
    objective: "Step 2 objective.",
    points: ["Point 1 for Step 2", "Point 2 for Step 2"],
  },
  {
    title: "Step 3 Title",
    objective: "Step 3 objective.",
    points: ["Point 1 for Step 3", "Point 2 for Step 3"],
  },
  // Add more steps as needed
];

export default function Stepper() {
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < steps.length - 1) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="border rounded-lg p-6 shadow-md">
        <h2 className="text-2xl font-bold text-red-700">
          {steps[currentStep].title}
        </h2>
        <p className="text-gray-500 font-semibold mt-2">
          {steps[currentStep].objective}
        </p>
        <ul className="mt-4 space-y-2">
          {steps[currentStep].points.map((point, index) => (
            <li key={index} className="flex items-start gap-2">
              <span className="text-gray-700">💬</span>
              <p className="text-gray-700">{point}</p>
            </li>
          ))}
        </ul>
        <div className="mt-6 flex justify-between items-center">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={cn(
              "px-4 py-2 rounded-md text-white bg-gray-500 disabled:bg-gray-300",
              currentStep === 0 && "cursor-not-allowed"
            )}
          >
            Previous
          </button>
          <span className="text-gray-500">{`${currentStep + 1} OF ${
            steps.length
          }`}</span>
          <button
            onClick={nextStep}
            disabled={currentStep === steps.length - 1}
            className={cn(
              "px-4 py-2 rounded-md text-white bg-blue-500 disabled:bg-gray-300",
              currentStep === steps.length - 1 && "cursor-not-allowed"
            )}
          >
            Next
          </button>
        </div>
      </div>

      <div className="mt-6 flex justify-between">
        {steps.map((_, index) => (
          <div key={index} className="flex-1">
            <div
              className={cn(
                "h-1 rounded-full transition-all duration-300",
                index <= currentStep ? "bg-red-700" : "bg-gray-300"
              )}
            ></div>
            <p className="text-center mt-2 text-sm font-semibold">
              Step {index + 1}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
