"use client";
import { useEffect, useState } from "react";
import TopBanner from "@/components/topbanner";
import { Headder } from "@/components/navBar";

const TopBannerWrapper = () => {
  const [close, setClose] = useState(false);
  const [data, setData] = useState(null);
  useEffect(() => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
    const fetchData = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/subscriptions`);
        const result = await response.json();
        // console.log("API Response:", result);
        setData(result); // Store the data in state
      } catch (error) {
        // console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="sticky top-0 z-50">
      <TopBanner close={close} setClose={setClose} />
      <Headder close={close} />
    </div>
  );
};

export default TopBannerWrapper;
