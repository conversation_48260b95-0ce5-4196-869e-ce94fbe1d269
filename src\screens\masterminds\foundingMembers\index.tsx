// components/FoundingMembers.tsx
import React from "react";
import { Linkedin } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
const members = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Associate: Quantitative Researcher",
    description:
      "A graduate in Mathematics & Computing from IIT Kharagpur, <PERSON><PERSON><PERSON><PERSON> brings a strong background in mathematical modeling and machine learning. His journey includes research stints at UCLA and contributions to open-source projects through the Google Summer of Code. Previously, at Microsoft, he worked on building deep learning-based speech-to-text models tailored for Indian languages.",
    image:
      "https://res.cloudinary.com/damm9iwho/image/upload/v1739436080/Shreyas_xhh0pm.svg",
    linkedIn: "https://www.linkedin.com/in/shreyas-kowshik-115716155/",
  },
  {
    name: "<PERSON><PERSON>",
    role: "Analyst: Quantitative Developer",
    description:
      "<PERSON><PERSON> is a BITS <PERSON> graduate holding a master’s degree in Mathematics. With experience in trading fixed income securities at Futures First and quant research at True Beacon, <PERSON><PERSON> excels in analyzing market data. <PERSON><PERSON> has a passion for quantitative trading and generating alphas.",
    image: "/assets/team/Anay <PERSON>.jpg",
    linkedIn: "https://www.linkedin.com/in/anay-kale-*********/",
  },
  {
    name: "Harkirat Dhiman",
    role: "Associate: Finance & Accounting",
    description:
      "Harkirat is an enthusiastic Chartered Accountant and a commerce graduate from Panjab University. Proficient in stakeholder communication, leadership, and coordination, Harkirat has a strong track record in managing tax litigation, statutory audits & financial reporting.",
    image:
      "https://res.cloudinary.com/damm9iwho/image/upload/v1739436075/Dhiman_ndcfzs.svg",
    linkedIn: "https://www.linkedin.com/in/ca-harkirat-dhiman-50808a31b/",
  },
];

const FoundingMembers = () => {
  return (
    <>
      <div className="mx-auto p-6 max-md:p-4 mt-20 max-md:mt-12 mb-4">
        <h2 className="heading text-center ">Investors & Advisors</h2>
      </div>
      <div className="row justify-center mt-0 max-md:-mt-4 ">
        <div className="flex justify-center">
          <div className="grid -md:grid-cols-1 md:grid-cols-3 text-center justify-center ">
            <div className="my-10 -md:order-2">
              <div className="cardInvest">
                <div className="card__image">
                  <img
                    src="/assets/team/Aditi Kothari.jpg"
                    alt="Aditi Kothari"
                  />
                </div>

                <div className="card__content">
                  <span className="card__title">Aditi Kothari</span>
                  <span className="font-medium text-primary">Investor</span>
                  <span className="font-medium text-primary">
                    Chairperson - DSP Mutual Fund
                  </span>

                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button>LinkedIN</Button> */}
                </div>
              </div>
            </div>
            <div className="my-10 max-md:my-0 -md:order-1">
              <div className="cardInvest ">
                <div className="card__image">
                  <img
                    src="/assets/team/Kalpen Parekh.jpg"
                    alt="Kalpen Parekh"
                  />
                </div>

                <div className="card__content">
                  <span className="card__title">Kalpen Parekh</span>
                  <span className="font-medium text-primary">
                    Board Observer
                  </span>
                  <span className="font-medium text-primary">
                    CEO - DSP Mutual Fund
                  </span>
                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button>LinkedIN</Button> */}
                </div>
              </div>
            </div>
            <div className="my-10 -md:order-3 ">
              <div className="cardInvest ">
                <div className="card__image">
                  <img src="/assets/team/Kunal Bajaj.jpg" alt="Kunal Bajaj" />
                </div>

                <div className="card__content">
                  <span className="card__title">Kunal Bajaj</span>
                  <span className="font-medium text-primary">
                    Board Advisor
                  </span>
                  <span className="font-medium text-primary">
                    MD - Pulsar Capital
                  </span>
                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button color="primary">
                    <img
                      src="../assets/icons/linkedin-logo.svg"
                      width={25}
                      style={{ filter: "brightness(0) invert(1)" }}
                    ></img>
                  </Button> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className=" mx-auto p-6 max-md:p-4 mt-32 max-md:mt-12 mb-12">
        <h2 className="heading mb-12 text-center max-md:mb-6">
          Founding Members
        </h2>
        <div className="space-y-10">
          {members.map((member, index) => (
            <div
              key={index}
              className="pb-6 max-md:pb-0 flex flex-col md:flex-row  gap-6 max-md:items-start"
            >
              <img
                src={member.image}
                alt={member.name}
                className="min-w-60 max-md:max-w-full rounded-lg object-cover aspect-square max-md:-mb-3"
              />
              <div className="">
                <h3 className="text-xl font-bold">{member.name}</h3>
                <p className=" font-semibold mb-2">{member.role}</p>
                <p className="text-gray-700 mb-4">{member.description}</p>
                <Link href={member.linkedIn} target="_blank">
                  <img src="/assets/inblack.svg" alt="" />
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FoundingMembers;
