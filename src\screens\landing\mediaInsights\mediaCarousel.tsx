import { <PERSON><PERSON> } from "@/components/ui/button";
import MediaCardComp from "./card";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
const data = [
  {
    img: "https://res.cloudinary.com/damm9iwho/image/upload/v1739430223/image_lsz4de.svg",
    heading: "DSP leads $2 Mn seed round in ArthAlpha",
    description:
      "“With the support of our investors, we are accelerating the development of AI-driven investment research platforms that will empower us to deliver insights and strategies that are not only smarter but also more personalized. As we scale our offerings and broaden our reach, we are excited to lead the charge in transforming investment management for the future”",
    auther: "- <PERSON><PERSON><PERSON>",
    type: "News",
    link: "https://entrackr.com/snippets/dsp-leads-2-mn-seed-round-in-arthalpha-8713124",
  },
  {
    img: "assets/article/image (5).png",
    heading:
      "Budget 2025 Expectations Highlights: Income tax slabs, rates to be revised under new tax regime?",
    description:
      "“The fintech sector eagerly looks to the Union Budget for policies that drive innovation, financial inclusion, and digital transformation. Key priorities include tax parity between capital gains on listed and unlisted securities to boost startup investments and revised taxation structures to ease operations for startups and fintech firms. Reducing personal income tax for individuals earning up to ₹50 lakh can increase disposable income, spurring consumption and investment. Rationalizing GST rates and easing compliance burdens for businesses, especially MSMEs, will streamline operations and fuel growth. Enhanced funding for financial literacy programs and rural digital infrastructure will extend the reach of digital finance. Simultaneously, ramping up infrastructure spending is essential to improve connectivity, logistics, and the overall ease of setting up and scaling businesses in the country’s growing digital economy. Expanding regulatory sandboxes for blockchain and AI-driven platforms will foster innovation while safeguarding consumers. Simplified norms for digital payments and incentives to extend UPI’s global reach can position India as a fintech powerhouse. Additionally, a robust focus on data privacy and improving credit access for MSMEs will further accelerate growth. A progressive, fintech-focused budget, backed by increased infrastructure investments, has the potential to transform India’s digital economy into a resilient, future-ready ecosystem,” says Rohit Beri, CEO and CIO, ArthAlpha.”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://timesofindia.indiatimes.com/business/india-business/budget-2025-expectations-live-updates-date-income-tax-slab-salaried-employees-stock-market-railways-defence-real-estate-economic-survey/liveblog/*********.cms",
  },
  {
    img: "assets/article/image (6).png",
    heading:
      "India’s Union Budget 2025: Expert Predictions on Digital Transformation, Infrastructure, and Startup Growth",
    description:
      "“As the fintech sector eagerly awaits the Union Budget, we anticipate forward-thinking policies that drive innovation, financial inclusion, and digital transformation. Key expectations include revised taxation structures for startups and fintech firms, promoting ease of doing business through tax parity between capital gains for listed and unlisted securities, which will spur investment in the startup ecosystem. Enhanced budgetary allocations for financial literacy programs and digital infrastructure in rural and semi-urban areas will further expand the reach of digital finance. The introduction of a regulatory sandbox expansion for emerging technologies like blockchain and AI-driven lending platforms would foster innovation while ensuring consumer protection. Additionally, streamlined compliance norms for digital payments and incentives for expanding UPI penetration to international markets can position India as a global fintech leader. A holistic approach to data privacy regulations and credit access for MSMEs would further bolster growth. A dynamic, fintech-centric budget can strengthen India’s digital economy, making it more resilient and future-ready.”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://startuptalky.com/india-union-budget-2025-expert-insights-industry-impact/",
  },
  {
    img: "assets/article/image (7).png",
    heading:
      "Union Budget 2025 Live: New Income Tax Bill To Be Tabled In Parliament This Week; Key Changes Expected",
    description:
      "“The 2025-26 Budget brings much-needed clarity and efficiency to the taxation of mutual funds, ensuring a balanced and investor-friendly framework. With rationalized capital gains taxation across equity and debt funds, higher tax-free thresholds, and revised TDS/TCS provisions, these reforms significantly reduce compliance burdens for both investors and asset management companies. By incentivizing long-term investment through favorable tax treatment and aligning mutual fund taxation with global best practices, the government is fostering deeper retail participation, enhancing liquidity in capital markets, and ensuring a stable flow of capital into the economy. These changes will not only strengthen investor confidence but also position mutual funds as a key vehicle for wealth creation and financial inclusion in India's growth story.”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.goodreturns.in/news/union-budget-2025-live-updates-fm-likely-to-announce-major-tax-reliefs-on-february-1-1402565.html",
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733726518/arthalpha/image3_wjxyvq.svg",
    heading:
      "Where will the Indian Rupee head after a fresh low against the US Dollar?",
    description:
      "“Trump’s administration previously promoted pro-India policies that strengthened trade and investment ties, and this established rapport could foster more favourable trade terms, boost bilateral investments, and potentially stabilize foreign inflows into India. Such an alignment would benefit both markets, as stronger economic cooperation could attract investment, drive trade, and support broader financial stability.”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.theweek.in/news/biz-tech/2024/11/08/inr-vs-usd-indian-rupee-depreciation.html",
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733726519/arthalpha/image_zp5m9b.svg",
    heading:
      "Saving for your child’s education abroad? Key advice from financial experts",
    description:
      "“Planning for your child's education abroad involves careful budgeting and investment strategies. With rising costs due to inflation and currency fluctuations, experts recommend starting early”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.livemint.com/money/personal-finance/saving-for-your-child-s-education-abroad-key-advice-from-financial-experts-mutual-funds-investment-11732601693443.html",
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734528429/arthalpha/new_hp5xny.svg",
    heading:
      "New pension scheme for minors: Should you invest in NPS Vatsalya?",
    description:
      "“The NPS Vatsalya, in my opinion, is not an attractive investment choice. The 75% cap on equity investments is too conservative, especially considering the long lock-in period. Additionally, the restrictions on withdrawals for life events like education, marriage, and home purchases make it less appealing as a savings tool for such milestones. However, it can be a solid option for those looking for a structured, secure retirement plan for their special-needs children or for individuals wanting to instill disciplined savings early on for retirement. It offers the dual benefits of tax efficiency and a steady pension payout.”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.livemint.com/money/personal-finance/nps-vatsalya-new-pension-scheme-for-minors-should-you-invest-in-it-savings-retirement-national-pension-system-wealth/amp-11731904659651.html",
  },

  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734528429/arthalpha/new_hp5xny.svg",
    heading:
      "नाबालिगों के लिए नई पेंशन योजना: क्या NPS वात्सल्य में निवेश करें?",
    description: `“मेरे विचार से NPS वात्सल्य एक आकर्षक निवेश विकल्प नहीं है। इक्विटी निवेश पर 75% की लिमिट बहुत कंज़र्वेटिव है, खासकर लंबी लॉक-इन अवधि को देखते हुए। इसके अलावा, शिक्षा, विवाह और घर खरीदने जैसी चीजों के लिए निकासी पर पाबंधी इसे सेविंग इंस्ट्रूमेंट के रूप में कम आकर्षक बनाते हैं।
हालांकि, यह उन लोगों के लिए एक अच्छा विकल्प हो सकती है जो अपने 'स्पेशल (शारीरिक या मानसिक कमजोरी)' बच्चों के लिए एक स्ट्रक्चर्ड और सिक्योर रिटायरमेंट प्लॉन की तलाश कर रहे हैं या उन व्यक्तियों के लिए जो जल्दी रिटायरमेंट लेने के मक़सद से अनुशासित बचत करना चाहते हैं। यह टैक्स एफिशंसी और एक स्थिर पेंशन पे-ऑउट के दोहरे लाभ प्रदान करती है।”`,
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.livemint.com/hindi/market/new-pension-scheme-for-minors-should-you-invest-in-nps-vatsalya-241731932377530.html",
  },

  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734701863/arthalpha/ai_ixx7b9.svg",
    heading: "AI-Powered Cyberattacks Top Concern for Executives in 2024",
    description: `“In asset management, the rise of AI-enhanced cyber threats brings significant risk, as attackers exploit sensitive data and critical financial systems. However, AI and deep learning also play a pivotal role in defending against these threats. Leveraging machine learning models for real-time anomaly detection, automated response systems, and predictive analytics can help preempt attacks by recognizing suspicious patterns and weak points.”`,
    auther: "- Rohit Kumar Jha",
    type: "Article",
    link: "https://www.entrepreneur.com/en-in/news-and-trends/ai-powered-cyberattacks-top-concern-for-executives-in-2024/482523",
  },

  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734701863/arthalpha/block_gjum8w.svg",
    heading: "Blockchain: Game Changer In Constructing Data Security In India",
    description: `“In the construction industry, where sensitive project information flows across multiple stakeholders, data security is not just a necessity—it’s a foundation for trust. Blockchain technology offers an innovative solution by creating a tamper-proof, transparent, and decentralized ledger that protects critical project data at every stage. Adopting blockchain is no longer optional but essential for fostering resilience, accountability, and long-term growth”.`,
    auther: "- Rohit Kumar Jha",
    type: "Article",
    link: "https://bwsecurityworld.com/technology/blockchain-game-changer-in-constructing-data-security-in-india/",
  },
];

const RecentProduct = (props: any) => {
  return (
    <>
      <div>
        <Carousel className="w-full mb-0">
          <CarouselContent className="overflow-visible">
            {data.slice(0, 6).map((item, index) => {
              return (
                <>
                  {props.mediaType == "All" ||
                  props.mediaType ==
                    (item.type == "Article" ? "Articles" : item.type) ? (
                    <CarouselItem
                      key={index}
                      className="md:basis-1/2 lg:basis-1/3 py-6"
                    >
                      <MediaCardComp
                        id={index}
                        item={item}
                        mediaType={props.mediaType}
                      />
                    </CarouselItem>
                  ) : (
                    <></>
                  )}
                </>
              );
            })}
          </CarouselContent>
          {/* Customize Previous Button */}
          <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-200 text-primary p-2 rounded-full" />

          {/* Customize Next Button */}
          <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-200 text-primary p-2 rounded-full" />
        </Carousel>

        <div className="flex justify-end mt-2 max-md:mt-0 max-md:justify-center">
          <Link href="/articles">
            <Button
              color="primary"
              variant="outline"
              size="sm"
              className=" rounded-full border-borderColor border-[1px]"
            >
              View All
              <ArrowRight
                size={16}
                className=""
                style={{ strokeWidth: "1px" }}
              />
            </Button>
          </Link>
        </div>
      </div>
    </>
  );
};

export default RecentProduct;
