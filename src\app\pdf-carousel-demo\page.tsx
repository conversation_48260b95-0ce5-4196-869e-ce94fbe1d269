"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

const PdfViewer = dynamic(() => import("@/components/PdfViewer"), {
  ssr: false, // Prevents hydration issues
});

export default function PdfCarouselDemo() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return <div>Loading PDF Viewer...</div>;

  return (
    <div>
      <div className="p-4 md:p-8">
        <div className="mb-4">
          <h1 className="text-2xl font-bold mb-2">PDF Carousel Demo</h1>
          <p className="text-gray-600 mb-4">
            <strong>Mobile:</strong> Shows one page at a time with smooth swipe
            gestures. Swipe left for next page, swipe right for previous page.
            Vertical scrolling works normally for website navigation.
          </p>
          <p className="text-gray-600">
            <strong>Desktop:</strong> Shows the full PDF viewer with toolbar and
            all standard features.
          </p>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              📱 <strong>Mobile Features:</strong> Single-page view • Horizontal
              swipe navigation • Smooth transitions • Touch-friendly buttons •
              Page indicators • Normal vertical scrolling preserved
            </p>
          </div>
          <div className="mt-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
            <p className="text-sm text-purple-800">
              🖥️ <strong>True Fullscreen Presentation:</strong> Takes over
              entire screen (not just browser) • Auto landscape orientation on
              mobile • Auto-play slideshow • Keyboard controls (F to enter, ESC
              to exit) • Professional presentation experience
            </p>
          </div>
          <div className="mt-2 p-3 bg-green-50 rounded-lg border border-green-200">
            <p className="text-sm text-green-800">
              ⚡ <strong>Smart Zoom & Navigation:</strong> Pinch-to-zoom (1x-3x)
              • When zoomed: drag to pan, swipe disabled • When normal: 4-way
              swipe navigation • +/- keys for zoom • 0 key to reset • Auto-hide
              UI after 3 seconds
            </p>
          </div>
        </div>
        <PdfViewer fileUrl="/pdf/Arthalpha Brand-Deck.pdf" />
      </div>
    </div>
  );
}
