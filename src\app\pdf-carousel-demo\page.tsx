"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

const PdfViewer = dynamic(() => import("@/components/PdfViewer"), {
  ssr: false, // Prevents hydration issues
});

export default function PdfCarouselDemo() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) return <div>Loading PDF Viewer...</div>;
  
  return (
    <div>
      <div className="p-4 md:p-8">
        <div className="mb-4">
          <h1 className="text-2xl font-bold mb-2">PDF Carousel Demo</h1>
          <p className="text-gray-600">
            On mobile devices, this PDF viewer will display in carousel style with navigation buttons and page indicators.
            On desktop, it shows the full PDF viewer with toolbar.
          </p>
        </div>
        <PdfViewer fileUrl="/pdf/Arthalpha Brand-Deck.pdf" />
      </div>
    </div>
  );
}
