import LandingHero from "./hero";
import AmChart from "./hero/amChartMap";
import Journey from "./journey";
import LandingMediaInsights from "./mediaInsights";
import LandingOurTeem from "./ourTeem";
import FeaturesSection from "./whatSets";
import Landing<PERSON>ho from "./who";
import LandingWhy from "./why";
const Landing = () => {
  return (
    <>
     

      <div>
        <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow ">
          <LandingHero />
        </div>
        {/* <div className="h-screen">
          <AmChart />
        </div> */}
        <div
          className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow "
          id="read-more"
        >
          <LandingWho />
        </div>
        <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow ">
          <LandingWhy />
        </div>
        <div className="">
          <FeaturesSection />
        </div>
        <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow ">
          <Journey />
        </div>
        <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow ">
          <LandingOurTeem />
        </div>
        <div className="container mx-auto max-w-7xl px-6 max-md:px-4 flex-grow ">
          <LandingMediaInsights />
        </div>
      </div>
    </>
  );
};

export default Landing;
