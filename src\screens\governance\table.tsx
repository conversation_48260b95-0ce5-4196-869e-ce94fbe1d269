// const DisTable = () => {
//   return (
//     <>
//       <div>
//         <div className="tablecontainer">
//           <div className="scrollable-table hide-scroll-bar">
//             <table className="rounded-sm">
//               <thead style={{ height: "30px" }} className="rounded-md">
//                 <tr>
//                   <th className="text-center max-md:text-left">
//                     <input
//                       type="checkbox"
//                       className="checkbox checkbox-sm  checkbox-primary"
//                     />
//                   </th>
//                   <th>
//                     <div className="between">
//                       <p className="head-text">person</p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th className="text-center max-md:text-left">
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Incentive status
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th>
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Recipient email
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th>
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Incentive Type
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th className="text-center max-md:text-left">
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Incentive Amount
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th className="text-center max-md:text-left">
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Incentive sender
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th className="text-center max-md:text-left">
//                     <div className="between">
//                       <p className="head-text whitespace-nowrap">
//                         Incentive sent at
//                       </p>
//                       <i className="icon-small-right ph ph-caret-up-down ml-5"></i>
//                     </div>
//                   </th>
//                   <th></th>
//                 </tr>
//               </thead>
//               <tbody className="max-md:grid max-sm:grid-cols-1 max-md:grid-cols-2">
//                 <tr className="max-md:grid max-sm:grid-cols-2 max-md:grid-cols-2">
//                   <td className="text-center max-md:text-left  m-0 max-md:col-span-2">
//                     <input
//                       type="checkbox"
//                       className="checkbox checkbox-sm  checkbox-primary m-0"
//                     />
//                   </td>
//                   <td className="max-md:col-span-2">Alisha</td>
//                   <td>p</td>
//                   <td><EMAIL></td>
//                   <td></td>
//                   <td></td>
//                   <td></td>
//                   <td></td>
//                   <td>
//                     <div className="dropdown">
//                       <label
//                         className="btn btn-primary btn-sm btn-ghost"
//                         // tabindex="0"
//                       >
//                         <i className="ph-fill ph-dots-three-outline"></i>
//                       </label>
//                       <div className="dropdown-menu max-lg:dropdown-menu-top-right">
//                         <span
//                           // tabindex="-1"
//                           className="dropdown-item text-sm"
//                         >
//                           {/* <label className="cursor-pointer" for="add-funds">
//                             Send Incentive
//                           </label> */}
//                         </span>
//                       </div>
//                     </div>
//                   </td>
//                 </tr>
//               </tbody>
//             </table>
//           </div>
//         </div>
//       </div>
//     </>
//   );
// };

// export default DisTable;
