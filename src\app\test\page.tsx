// app/page.tsx
import yahooFinance from "yahoo-finance2";

async function getCompanyData(symbols: string[]) {
  try {
    const results = await Promise.all(
      symbols.map((symbol) => yahooFinance.quote(symbol))
    );
    return results;
  } catch (error) {
    console.error("Error fetching Yahoo Finance data:", error);
    return [];
  }
}

export default async function HomePage() {
  const indiaCompany = process.env.NEXT_PUBLIC_INDIA_COMPANY || "";
  const uaeCompany = process.env.NEXT_PUBLIC_UAE_COMPANY || "";
  const indiaCompany2 = process.env.NEXT_PUBLIC_INDIA_COMPANY_NIFTY || "";

  const companies = await getCompanyData([
    indiaCompany,
    uaeCompany,
    indiaCompany2,
  ]);

  return (
    <div className="container mx-auto mt-8">
      <h1 className="text-2xl font-bold mb-4">Latest Company Data</h1>
      <div className="p-4">
        {companies.map((company, index) => (
          <div key={index} className="mb-4 border p-4 rounded shadow">
            <h2 className="text-lg font-bold">{company?.shortName}</h2>
            <div>
              <strong>Symbol:</strong> {company?.symbol}
            </div>
            <div>
              <strong>Price:</strong> {company?.regularMarketPrice}{" "}
              {company?.currency}
              {company?.regularMarketChange}
            </div>
            <div>
              <strong>Change:</strong> (
              {company?.regularMarketChangePercent?.toFixed(2)}%)
              {/* {company?.currency}
              {company?.regularMarketChange} */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
