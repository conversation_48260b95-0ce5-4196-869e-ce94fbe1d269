// app/api/getCompanyData/route.ts
import yahooFinance from "yahoo-finance2";

export async function GET(req: Request) {
  const url = new URL(req.url);
  const symbols = url.searchParams.get("symbols")?.split(",") || [];

  try {
    const results = await Promise.all(
      symbols.map((symbol: string) => yahooFinance.quote(symbol))
    );
    return new Response(JSON.stringify(results), { status: 200 });
  } catch (error) {
    console.error("Error fetching Yahoo Finance data:", error);
    return new Response("Failed to fetch data", { status: 500 });
  }
}
