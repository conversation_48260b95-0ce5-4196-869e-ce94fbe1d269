"use client";
import Image from "next/image";
import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import GetInTouch from "./getInTouch";
import { Input } from "@nextui-org/input";
import { But<PERSON> } from "./ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>eader,
  useDisclosure,
} from "@nextui-org/react";

const Footer = () => {
  const pathname = usePathname();
  const caseStudy = pathname.slice(1, 6);
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState("");
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch(`${API_BASE_URL}/subscribe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        setStatus("Email sent successfully!");
        if (result?.success) {
          onOpen();
          setEmail(" ");
        }
        console.log(result);
      } else {
        setStatus(result.error || "Failed to send email.");
      }
    } catch (error) {
      console.error("Error:", error);
      setStatus("An error occurred.");
    }
  };

  return (
    <>
      {/* {caseStudy == "artha" ? (
        <div>
          <p>footer data</p>
        </div>
      ) : ( */}
      <div>
        <div className="container mx-auto max-w-7xl px-6  max-md:px-4 flex-grow">
          <GetInTouch />
        </div>
        <footer className="">
          <div className="mx-auto max-w-screen-xl space-y-8 px-4 py-16 sm:px-6 lg:space-y-16 lg:px-8">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-6">
              <div className="lg:col-span-2 pr-8">
                <div className="text-teal-600">
                  <Image
                    src={"/assets/artha.svg"}
                    alt={""}
                    width={120}
                    height={120}
                    className=""
                  />
                </div>

                <div className="mt-4">
                  <p>LLP Identification Number - ACC-3208</p>
                  <p className=" md:whitespace-nowrap">
                    GST Registration Number - 29ACCFA7784K1ZS
                  </p>
                  <p>SEBI Registration Number - INP000009038</p>
                  <p className="mt-3">
                    #2053 Prestige White Meadows, Whitefield, Bangalore, 560066
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-8 sm:grid-cols-1 lg:col-span-4 lg:grid-cols-4 max-md:gap-6">
                <div className="lg:flex lg:flex-row lg:justify-end">
                  <div>
                    <p className="font-semibold text-gray-900 mt-3">
                      Investment Services
                    </p>

                    <ul className="mt-4 space-y-4 text-sm max-md:space-y-3">
                      <li>
                        <Link
                          href="/governance"
                          className="text-gray-700 transition hover:opacity-75"
                        >
                          {" "}
                          Governance
                        </Link>
                      </li>

                      <li>
                        <a
                          href="https://smartodr.in/login"
                          className="text-gray-700 transition hover:opacity-75"
                          target="_blank"
                        >
                          {" "}
                          Smart ODR
                        </a>
                      </li>
                      <li>
                        <a
                          href="https://scores.sebi.gov.in/"
                          className="text-gray-700 transition hover:opacity-75"
                          target="_blank"
                        >
                          {" "}
                          SEBI Scores portal
                        </a>
                      </li>
                      <li>
                        <Link
                          href="/faq"
                          className="text-gray-700 transition hover:opacity-75"
                        >
                          {" "}
                          FAQ
                        </Link>
                      </li>
                      <li>
                        <a
                          href="https://docs.google.com/spreadsheets/d/1aWwHvQpc3v-I93Lx7BKgMfPjf5Dyc1GwmjWAKPQ4Q78/edit?usp=sharingr"
                          className="text-gray-700 transition hover:opacity-75"
                          target="_black"
                        >
                          {" "}
                          Fee Calculator
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="lg:flex lg:flex-row lg:justify-center">
                  <div>
                    <p className="font-semibold text-gray-900 mt-3 ">Company</p>

                    <ul className="mt-4 space-y-4 text-sm max-md:space-y-3">
                      <li>
                        <a
                          href="/why"
                          className="text-gray-700 transition hover:opacity-75"
                        >
                          {" "}
                          About Us{" "}
                        </a>
                      </li>

                      <li>
                        <a
                          href="/investmentPhilosophy"
                          className="text-gray-700 transition hover:opacity-75"
                        >
                          {" "}
                          Our Philosophy{" "}
                        </a>
                      </li>
                      <li>
                        <a
                          href="/contactUs"
                          className="text-gray-700 transition hover:opacity-75"
                        >
                          {" "}
                          Contact Us
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="md:col-span-2">
                  <div>
                    <p className="font-semibold text-primary mt-3">
                      Subscribe to our newsletter
                    </p>

                    <div>
                      <div className=" flex flex-row items-center gap-3 mt-4 shadow-none">
                        <Input
                          type="email"
                          // label="Email"
                          placeholder="Email ID"
                          // labelPlacement="outside"
                          className=" rounded-lg bg-[#FAF5F5] shadow-none h-12"
                          style={{ paddingRight: 0, boxShadow: "none" }}
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          size="lg"
                          endContent={
                            <Button
                              className="bg-primary text-white -mr-2"
                              onClick={handleSubmit}
                            >
                              Register
                            </Button>
                          }
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="font-semibold text-primary mt-3">Social</p>

                    <div className="row gap-3 mt-2">
                      <Link
                        href="https://www.linkedin.com/company/arthalpha/"
                        target="_blank"
                      >
                        <img
                          src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733727301/arthalpha/devicon_linkedin_ik8ban.svg"
                          alt=""
                          className="w-8 h-8"
                        />
                      </Link>
                      <Link href="https://x.com/arthalpha" target="_blank">
                        <img
                          src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733727301/arthalpha/twitter_gkpnfh.svg"
                          alt=""
                          className="w-8 h-8"
                        />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr></hr>
            <div>
              <div className="text-sm text-gray-500 text-center lg:-my-12">
                Copyright © 2024 ArthAlpha |{" "}
                <span className=" cursor-pointer">All Rights Reserved</span> |{" "}
                <span className="cursor-pointer">
                  <Link
                    href="/pdf/AA_Disclaimer and Privacy Policy.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Disclaimer and Privacy Policy
                  </Link>
                </span>
              </div>
            </div>
          </div>
        </footer>
      </div>
      {/* )} */}

      <div>
        <Modal
          isDismissable={false}
          isKeyboardDismissDisabled={true}
          isOpen={isOpen}
          backdrop="blur"
          onOpenChange={onOpenChange}
          placement="center"
          className="w-[80%] h-[80%] min-w-[80%] min-h-[80%] max-md:w-[95%] max-md:h-[70%] max-md:min-w-[95%] max-md:min-h-[70%] rounded-[3rem]"
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalBody className="rounded-[3rem]" style={{ padding: 0 }}>
                  <div className="relative rounded-[3rem] w-full h-full py-12 max-md:py-6 flex items-center justify-center  primary-gradient">
                    <div className="absolute top-6 right-0 w-64 h-64 rounded-bl-full rounded-[3rem]">
                      <img
                        src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/Gradient_zcuwdx.svg"
                        alt=""
                        width="200"
                        height="200"
                        className="  w-72 h-72 "
                      />
                    </div>

                    <div className="relative   bg-white-7 border border-white/100 backdrop-blur-md rounded-[3rem] px-6 py-12 max-md:py-10 max-md:mx-8 text-center max-w-lg w-full text-white shadow-lg">
                      <h2 className="text-3xl font-bold">THANK YOU!</h2>

                      <div className="flex justify-center mt-4">
                        <img
                          src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/image_26_i4zdma.png"
                          alt=""
                          width="200"
                          height="200"
                        />
                      </div>

                      <p className="mt-4 text-lg px-12">
                        Thank you for subscribing!
                        <br />
                        We look forward to sharing updates and insights with
                        you.
                      </p>

                      <Button
                        className="text-black bg-white hover:bg-white hover:text-black rounded-xl font-bold h-9 mt-4"
                        onClick={onClose}
                      >
                        Close
                      </Button>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default Footer;
