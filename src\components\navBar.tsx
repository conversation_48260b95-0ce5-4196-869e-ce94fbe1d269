"use client";
import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Navbar<PERSON>ontent,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@nextui-org/navbar";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@nextui-org/dropdown";
import { Button } from "./ui/button";
import Link from "next/link";
import clsx from "clsx";

import { useEffect, useState } from "react";
// import { NavigationMenuDemo } from "./menuList";
import { ChevronDown } from "lucide-react";
import { Accordion, AccordionItem } from "@nextui-org/react";

export const AcmeLogo = () => {
  return (
    <svg fill="none" height="36" viewBox="0 0 32 32" width="36">
      <path
        clipRule="evenodd"
        d="M17.6482 10.1305L15.8785 7.02583L7.02979 22.5499H10.5278L17.6482 10.1305ZM19.8798 14.0457L18.11 17.1983L19.394 19.4511H16.8453L15.1056 22.5499H24.7272L19.8798 14.0457Z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );
};

export const Headder = ({ close }: any) => {
  const [isDarkSection, setIsDarkSection] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const [loading, setLoading] = useState(true); // Loading state to control navbar visibility
  const [hideOnClick, setHideOnClick] = useState(true);

  useEffect(() => {
    // Set a timer to hide the loading state after 3 seconds
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800); // 3-second delay

    return () => clearTimeout(timer); // Clean up the timer on unmount
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const isInView = entries.some((entry) => entry.isIntersecting);
        setIsDarkSection(isInView);
      },
      { threshold: 0.1 }
    );

    // Observe all elements with the "dark-section" class
    const darkSections = document.querySelectorAll(".dark-section");
    darkSections.forEach((section) => observer.observe(section));

    return () => {
      darkSections.forEach((section) => observer.unobserve(section));
    };
  }, []);

  const itemClasses = {
    base: "py-0 w-full",
    title: "font-[600] text-base",
    indicator: "text-base",
    content: "text-base",
  };
  const itemWeb = {
    base: "py-0 w-full",
    title: "text-base",
    indicator: "text-base",
    content: "text-base",
  };

  return (
    <div>
      {!loading && (
        <NextUINavbar
          maxWidth="xl"
          position="sticky"
          style={{}}
          onMenuOpenChange={setIsMenuOpen}
          isMenuOpen={isMenuOpen}
          isBlurred
          className={clsx(
            "mx-[20rem] py-0 w-auto px-0 max-lg:mx-20 max-md:mx-2 max-xl:mx-40 max-2xl:mx-[18rem] border-2  items-center rounded-2xl sticky top-0 mt-3 max-md:mt-2 h-[50px]",
            { "text-white": isDarkSection, "text-black": !isDarkSection }
          )}
        >
          <NavbarContent justify="start">
            <NavbarMenuToggle
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              className="md:hidden pr-3 "
            />
            <NavbarBrand className="pb-2 ">
              {" "}
              <Link href="/" onClick={() => setIsMenuOpen(false)}>
                {" "}
                <img src="/assets/artha.svg" alt="Logo" className="h-[36px]" />
              </Link>
            </NavbarBrand>
          </NavbarContent>
          <NavbarContent justify="center">
            <div className="row gap-2 max-md:hidden ">
              <Dropdown>
                <DropdownTrigger>
                  <Button variant="ghost" className="p-1 font-[600] ">
                    About Us
                    <span className="-ml-1 mt-[2px]">
                      <ChevronDown strokeWidth="3px" />
                    </span>
                  </Button>
                </DropdownTrigger>
                <DropdownMenu
                  aria-label="Static Actions"
                  className="bg-white  rounded-xl"
                >
                  <DropdownItem showDivider key="new" className="px-3 py-2 ">
                    <Link href="/why" className="w-full block">
                      About ArthAlpha
                    </Link>
                  </DropdownItem>
                  <DropdownItem showDivider key="copy" className=" px-3 py-2">
                    <Link href="/investmentPhilosophy" className="w-full block">
                      Our Philosophy
                    </Link>
                  </DropdownItem>
                  <DropdownItem showDivider key="edit" className=" px-3 py-2">
                    <Link href="/investmentProcess" className="w-full block">
                      {" "}
                      Investment Process{" "}
                    </Link>
                  </DropdownItem>
                  <DropdownItem key="team" className="px-3 py-2">
                    <Link href="/mastermind" className="w-full block">
                      Our Team
                    </Link>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>

              {/* <Dropdown shouldCloseOnInteractOutside={(e) => true}>
                <DropdownTrigger>
                  <Button
                    variant="ghost"
                    className="p-1 font-[600]"
                    onClick={() => setHideOnClick(true)}
                    // aria-expanded={!hideOnClick ? "false" : "true"}
                    aria-expanded="false"
                  >
                    Products
                    <span className="-ml-1 mt-[2px] ">
                      <ChevronDown strokeWidth="3px" />
                    </span>
                  </Button>
                </DropdownTrigger>
                {hideOnClick && (
                  <DropdownMenu
                    aria-label="Static Actions"
                    className="rounded-xl min-w-[150px]"
                    closeOnSelect={!hideOnClick ? true : false}
                  >
                    <DropdownItem showDivider key="copy" className=" py-0">
                      <Accordion
                        itemClasses={itemWeb}
                        className="px-1"
                        isCompact
                      >
                        <AccordionItem
                          key="ML"
                          aria-label="
                        India Equities (Long Only)
                  "
                          title="
                        India Equities (Long Only)
                  "
                        >
                          <div
                            className="mobileNavItem"
                            onClick={() => {
                              setIsMenuOpen(false), setHideOnClick(false);
                            }}
                          >
                            <Link href="/MLEquity" className="w-full block">
                              Machine Learning Equity Quant (MEQ)
                            </Link>
                          </div>
                        </AccordionItem>
                      </Accordion>
                    </DropdownItem>
                    <Button variant="ghost" className="p-1 font-[600]">
                    <Link href="/MLEquity">Product</Link>
                  </Button>

                  </DropdownMenu>
                )}
              </Dropdown> */}
              <Button variant="ghost" className="p-1 font-[600]">
                <Link href="/MLEquity">Product</Link>
              </Button>
              <Dropdown>
                <DropdownTrigger>
                  <Button variant="ghost" className="p-1 font-[600]">
                    Resources
                    <span className="-ml-1 mt-[2px] ">
                      <ChevronDown strokeWidth="3px" />
                    </span>
                  </Button>
                </DropdownTrigger>
                <DropdownMenu
                  aria-label="Static Actions"
                  className="bg-white rounded-xl min-w-[150px]"
                >
                  <DropdownItem
                    showDivider
                    key="new"
                    className=" px-3 py-2 hover:bg-[#F2F2F2]"
                  >
                    <Link href="/news" className="w-full block">
                      News
                    </Link>
                  </DropdownItem>
                  {/* <DropdownItem
                    showDivider
                    key="copy"
                    className="px-3 py-2 cursor-not-allowed opacity-40"
                  >
           
                    Blogs
        
                  </DropdownItem> */}
                  <DropdownItem key="edit" className="px-3 py-2">
                    <Link href="/articles" className="w-full block">
                      Articles
                    </Link>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
              <Button variant="ghost" className="p-1 font-[600]">
                <Link href="/contactUs">Contact Us</Link>
              </Button>
            </div>
          </NavbarContent>

          <NavbarContent justify="end">
            <NavbarItem className=" -mr-4">
              <Link
                href="https://eclientreporting.nuvamaassetservices.com/wealthspectrum/app/loginWith"
                target="_blank"
              >
                <Button className="bg-primary text-white rounded-xl h-9">
                  {/* Get Started */}
                  Investor Login
                </Button>
              </Link>
            </NavbarItem>
          </NavbarContent>

          <NavbarMenu className={close ? "-mt-2" : "max-md:mt-20"}>
            <div className="flex flex-col gap-2 px-0">
              <NavbarMenuItem className="px-0 -mx-1">
                <Accordion itemClasses={itemClasses}>
                  <AccordionItem
                    key="1"
                    aria-label="About Us"
                    title="About Us"
                    classNames={{
                      title: "text-base font-medium",
                      content: "text-sm",
                    }}
                  >
                    <div className="flex flex-col gap-2 px-0 pb-4">
                      <div className="mobileNavItem">
                        <Link href="/why" onClick={() => setIsMenuOpen(false)}>
                          About ArthAlpha
                        </Link>
                      </div>
                      <div className="mobileNavItem">
                        <Link
                          href="/investmentPhilosophy"
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full block"
                        >
                          Our Philosophy
                        </Link>
                      </div>
                      <div className="mobileNavItem">
                        <Link
                          href="/investmentProcess"
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full block"
                        >
                          Investment Process
                        </Link>
                      </div>
                      <div
                        className="mobileNavItem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Link href="/mastermind" className="w-full block">
                          Our Team
                        </Link>
                      </div>
                    </div>
                  </AccordionItem>
                </Accordion>
              </NavbarMenuItem>

              <NavbarMenuItem>
                <Button
                  variant="ghost"
                  className="p-1 font-[600] w-full text-left justify-start"
                >
                  <Link href="/MLEquity" onClick={() => setIsMenuOpen(false)}>
                    Product
                  </Link>
                </Button>
              </NavbarMenuItem>

              <NavbarMenuItem>
                <Accordion itemClasses={itemClasses}>
                  <AccordionItem
                    key="3"
                    aria-label="Resources"
                    title="Resources"
                    classNames={{
                      title: "text-base font-medium",
                      content: "text-sm",
                    }}
                  >
                    <div className="flex flex-col gap-2 px-0 pb-4">
                      <div className="mobileNavItem">
                        <Link
                          href="/news"
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full block"
                        >
                          News
                        </Link>
                      </div>
                      <div
                        className="mobileNavItem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Link href="/articles" className="w-full block">
                          Articles
                        </Link>
                      </div>
                    </div>
                  </AccordionItem>
                </Accordion>
              </NavbarMenuItem>

              <NavbarMenuItem>
                <Button
                  variant="ghost"
                  className="p-1 font-[600]"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Link href="/contactUs">Contact Us</Link>
                </Button>
              </NavbarMenuItem>
            </div>
          </NavbarMenu>
        </NextUINavbar>
      )}
    </div>
  );
};
