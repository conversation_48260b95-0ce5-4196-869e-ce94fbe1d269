import Image from "next/image";
import { Tooltip } from "@nextui-org/tooltip";

const points = [
  {
    id: "point1",
    left: "14%",
    top: "30%",
    label: "USA",
    time: "19 Dec, 2024 • 12:00 pm ist",
    data: [
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734672908/arthalpha/Frame_223532_lmewid.svg",
        title: "S&P 500",
        value: "$5,872.17",
        change: "−2.95%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734673013/arthalpha/Frame_2235334_w7nzfc.svg",
        title: "Dow Jones Industrial",
        value: "$42,326.88",
        change: "−2.58%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734629797/arthalpha/Frame_22353_s4gfzd.svg",
        title: "US Composite",
        value: "$19,392.69",
        change: "−3.56%",
        changeStatus: "loss",
      },
    ],
  }, // Point in North America
  {
    id: "point2",
    left: "48%",
    top: "15%",
    label: "UK",
    time: "19 Dec, 2024 • 12:00 pm ist",
    data: [
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675683/arthalpha/Frame_223531_eibyl1.svg",
        title: "FTSE 100 ",
        value: "£8,084.29",
        change: "−1.40%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223533_nwnzwg.svg",
        title: "FTSE All Share",
        value: "£4,417.36",
        change: "−1.38%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223534_u8eyaq.svg",
        title: "FTSE 250",
        value: "£21,285.83",
        change: "−1.17%",
        changeStatus: "loss",
      },
    ],
  }, // Point in Europe
  {
    id: "point3",
    left: "70%",
    top: "42%",
    label: "India",
    time: "19 Dec, 2024 • 12:00 pm ist",
    data: [
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675681/arthalpha/Frame_22353_2x456_wu704g.svg",
        title: "NIFTY 50",
        value: "₹23,951.70",
        change: "−1.02%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/83264_q8ald3.svg",
        title: "SENSEX",
        value: "₹79,218.05",
        change: "−1.20%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/74_wkklj8.svg",
        title: "NIFTY 500",
        value: "₹22,752.15",
        change: "−0.79%",
        changeStatus: "loss",
      },
    ],
  }, // Point in South Asia
  {
    id: "point4",
    left: "80%",
    top: "35%",
    label: "Thailand",
    time: "19 Dec, 2024 • 12:00 pm ist",
    data: [
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675681/arthalpha/Frame_2235321634_mgxszx.svg",
        title: "SSE Composite",
        value: "¥3,370.0331",
        change: "−0.36%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/Frame_22353273_bo4zss.svg",
        title: "Shenzhen",
        value: "¥10,649.0339",
        change: "+0.61%",
        changeStatus: "profit",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675680/arthalpha/Frame_22353765_uwtjly.svg",
        title: "SZSE Composite",
        value: "¥2,032.8495",
        change: "+0.36%",
        changeStatus: "profit",
      },
    ],
  }, // Point in Southeast Asia
  {
    id: "point5",
    left: "87%",
    top: "50%",
    label: "Philippines",
    time: "19 Dec, 2024 • 12:00 pm ist",
    data: [
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223535_zxzkkf.svg",
        title: "Japan 225",
        value: "¥38,813.36",
        change: "−0.69%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_22353_2x_v1t2ed.svg",
        title: "TOPIX",
        value: "¥2,713.83",
        change: "−0.22%",
        changeStatus: "loss",
      },
      {
        icon: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734675682/arthalpha/Frame_223536_cotjc5.svg",
        title: "Tokyo Stock Exchange",
        value: "¥1,396.67",
        change: "−0.22%",
        changeStatus: "loss",
      },
    ],
  }, // Point in the Philippines
];

const WorldMap = () => {
  return (
    <div className="relative w-full max-w-3xl mx-auto">
      <Image
        src="/assets/hero.svg" // Replace with your actual image path
        alt="World Map"
        width={800}
        height={400}
        layout="responsive"
      />

      {/* {points.map((point) => (
        <Tooltip key={point.id} content={point.label} placement="top">
          <div
            className="absolute w-2 h-2 bg-primary rounded-full cursor-pointer"
            style={{
              left: point.left,
              top: point.top,
              transform: "translate(-50%, -50%)",
            }}
          />
        </Tooltip>
      ))} */}
      {points.map((point) => (
        <Tooltip
          key={point.id}
          content={
            <div className="border-none rounded-3xl">
              <p className="text-sm mb-2 text-center text-[#929292]">
                {point.time}
              </p>
              {point.data.map((item, index) => (
                <div
                  className={
                    index % 2 == 0
                      ? "flex flex-row gap-6 items-start justify-between border-t-[1px] py-2 min-w-[320px]"
                      : "bg-[#F2F2F2] flex flex-row gap-6 items-start justify-between border-t-[1px] py-2 min-w-[320px]"
                  }
                >
                  <div className="row gap-2 pl-3">
                    <img
                      src={
                        item.icon
                          ? item.icon
                          : "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734629797/arthalpha/Frame_22353_s4gfzd.svg"
                      }
                      alt=""
                      className="w-8 h-8"
                    />
                    <p className="text-lg font-bold">{item.title}</p>
                  </div>
                  <div className="row gap-2 pr-3">
                    <p className="text-base font-bold">{item.value}</p>
                    <p
                      className={
                        item.changeStatus == "loss"
                          ? "text-danger"
                          : "text-[#17C964]"
                      }
                    >
                      ({item.change})
                    </p>
                  </div>
                </div>
              ))}
            </div>
          }
          placement="top"
          classNames={{
            base: "bg-white border border-gray-200 shadow-md p-0 rounded-lg",
          }}
        >
          <div
            className="absolute w-5 h-5 bg-primary bg-opacity-20 backdrop-blur-md  rounded-full cursor-pointer"
            style={{
              left: point.left,
              top: point.top,
              transform: "translate(-50%, -50%)",
            }}
          >
            <div className="relative w-5 h-5">
              {/* <div className="absolute w-5 h-5 bg-[#f48da7] bg-opacity-[0.001] backdrop-blur-3xl rounded-full"></div> */}
              <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-primary  rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
            </div>
          </div>
        </Tooltip>
      ))}
    </div>
  );
};

export default WorldMap;
