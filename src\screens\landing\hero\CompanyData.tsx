// components/CompanyData.tsx
import React from "react";

type CompanyDataProps = {
  data: {
    symbol: string;
    shortName: string;
    regularMarketPrice: number;
    currency: string;
  }[];
};

const CompanyData: React.FC<CompanyDataProps> = ({ data }) => {
  return (
    <div className="p-4">
      {data.map((company, index) => (
        <div key={index} className="mb-4 border p-4 rounded shadow">
          <h2 className="text-lg font-bold">{company.shortName}</h2>
          <p>
            <strong>Symbol:</strong> {company.symbol}
          </p>
          <div>
            <strong>Price:</strong> {company.regularMarketPrice}{" "}
            {company.currency}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CompanyData;
