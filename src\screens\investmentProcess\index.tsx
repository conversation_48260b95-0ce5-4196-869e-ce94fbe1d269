"use client";
import { Card, CardContent } from "@/components/ui/card";
import InvestmentProcessHero from "./hero";
import StepperComp from "./stepper";
import Stepper from "./stepper/test";
import Stepper1 from "./stepper/test1";

const InvestmentProcess = () => {
  return (
    <>
      <InvestmentProcessHero />
      <div className=" container mx-auto max-w-7xl px-6  max-md:px-4 flex-grow">
        <Card
          className="titleCard border-2 md:max-w-[85%] mx-auto mt-20 max-md:mt-16"
          style={{ padding: 0 }}
        >
          <CardContent className="flex flex-col justify-center items-center pt-12 px-10 max-md:px-4 max-md:pt-4 ">
            <p className="text-center pt-1 text-lg  font-[600]  leading-[26px] py-6 max-md:py-4">
              Our investment process embodies a disciplined, research-driven
              framework. Combining advanced machine learning techniques with
              timeless investment principles, we ensure a strategic edge in
              generating sustainable alpha.
            </p>
          </CardContent>
        </Card>
        <StepperComp />
      </div>
    </>
  );
};

export default InvestmentProcess;
