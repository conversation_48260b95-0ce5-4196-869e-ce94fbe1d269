const FAQHero = () => {
  const backgroundUrl = "/assets/heroImages/faq.png";
  return (
    <>
      <div
        className="grid grid-cols-2 max-md:grid-cols-1 h-[40vh] max-md:h-[25vh]   bg-white bg-hero"
        style={{
          backgroundImage: `
          linear-gradient(
            180deg,
            #ffffff 0%,
            rgba(255, 255, 255, 0.8) 72.22%,
            rgba(250, 245, 245, 0.1) 148.9%
          ),
          url(${backgroundUrl})
        `,

          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="flex flex-row items-center justify-center h-[40vh] max-md:h-[25vh] w-full col-span-2 ">
          <div className=" col-span-2 text-center">
            <p className="heroText">FAQ’s</p>

            <div className="max-md:content-center flex flex-row items-center justify-center">
              {/* <img src="/assets/artha.svg" alt="" /> */}
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center max-md:mt-0 max-md:pt-12">
          {/* <Image
            src="https://res.cloudinary.com/damm9iwho/image/upload/v1730211215/Objects_xwzfce.svg"
            alt={""}
            width={360}
            height={100}
            className="w-[80%] max-md:w-[100%] bg-transparent"
          /> */}
        </div>
      </div>
    </>
  );
};

export default FAQHero;
