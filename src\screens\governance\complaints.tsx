import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@nextui-org/table";

const govData1 = [
  {
    heading: "Data for the month ending",
    subheading:
      "^ Average Resolution time is the sum total of time taken to resolve each complaint in days, in the current month divided by total number of complaints resolved in the current month.",
    tableData: [
      {
        srNo: 1,
        receivedFrom: "Directly from Investors",
        pendingLastMonth: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
        pendingComplaintsOver3Months: 0,
        averageResolutionTimeDays: 0,
      },
      {
        srNo: 2,
        receivedFrom: "SEBI (SCORES)",
        pendingLastMonth: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
        pendingComplaintsOver3Months: 0,
        averageResolutionTimeDays: 0,
      },
      {
        srNo: 3,
        receivedFrom: "Other Sources (if any)",
        pendingLastMonth: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
        pendingComplaintsOver3Months: 0,
        averageResolutionTimeDays: 0,
      },
      {
        srNo: "Grand Total",
        receivedFrom: "",
        pendingLastMonth: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
        pendingComplaintsOver3Months: 0,
        averageResolutionTimeDays: 0,
      },
    ],
  },
];

const govData2 = [
  {
    heading: "Trend of monthly disposal of complaints",
    subheading:
      "* Inclusive of complaints of previous months resolved in the current month.\n# Inclusive of complaints pending as on the last day of the month.",
    tableData: [
      {
        srNo: 1,
        month: "April, 2025",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 2,
        month: "March, 2025",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 3,
        month: "February, 2025",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 4,
        month: "January, 2025",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 5,
        month: "December, 2024",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 6,
        month: "November, 2024",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 7,
        month: "October, 2024",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 8,
        month: "September, 2024",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: "Grand Total",
        month: "",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
    ],
  },
];

const govData3 = [
  {
    heading: "Trend of annual disposal of complaints",
    subheading:
      "** Inclusive of complaints of previous years resolved in the current year.\n## Inclusive of complaints pending as on the last day of the year.",
    tableData: [
      {
        srNo: 1,
        year: "2025-26",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 2,
        year: "2024-25",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 3,
        year: "2023-24",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: 4,
        year: "2022-23",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
      {
        srNo: "Grand Total",
        year: "",
        carriedForward: 0,
        received: 0,
        resolved: 0,
        totalPending: 0,
      },
    ],
  },
];
const Complaints = () => {
  return (
    <>
      <div className=" container max-w-5xl">
        <p className="text-2xl font-bold mb-4">Complaints</p>
        <p className="my-2">
          Client’s queries / complaints may arise due to lack of understanding
          or a deficiency of service experienced by clients. Deficiency of
          service may include lack of explanation, clarifications, understanding
          which escalates into shortfalls in the expected delivery standards,
          either due to inadequacy of facilities available or through the
          attitude of staff towards client.
        </p>
        {/* {data.map((item, index) => {
          return ( */}
        <div className="flex flex-row mb-1 items-start">
          <p className="text-4xl font-bold ml-2 mr-2 items-end text-end top-0 -mt-[1.2rem]">
            .
          </p>
          <p>
            Clients can see clarification to their query and are further
            entitled to make a complaint in writing, orally or telephonically.
            An email maybe sent for seeking clarification to the Client
            Servicing Team on{" "}
            <a href="mailto:<EMAIL>" className="text-[#800020]">
              <EMAIL>
            </a>{" "}
            and the clients may register their complaints on{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-[#800020]"
            >
              <EMAIL>
            </a>{" "}
            Alternatively, the Investor may call on{" "}
            <a href="tel:+918105916156" className="text-[#800020]">
              +91 81059 16156
            </a>
          </p>
        </div>
        {/* <div className="flex flex-row mb-1 items-start">
          <p className="text-4xl font-bold ml-2 mr-2 items-end text-end top-0 -mt-[1.2rem]">
            .
          </p>
          <p>
            Clients can seek clarification to their query and are further
            entitled to make a complaint in writing, orally or telephonically.
            An email may be sent to the Client Servicing Team on{" "}
            <a href="mailto:<EMAIL>" className="text-[#800020]">
              <EMAIL>
            </a>
            . Alternatively, the Investor may call on{" "}
            <a href="tel:+918105916156" className="text-[#800020]">
              +91 81059 16156
            </a>
          </p>
        </div> */}
        <div className="flex flex-row mb-1 items-start">
          <p className="text-4xl font-bold ml-2 mr-2 items-end text-end top-0 -mt-[1.2rem]">
            .
          </p>
          <p>
            A letter may also be written with their query/complaint and posted
            at the below mentioned address: #2053 Prestige White Meadows,
            Whitefield, Bangalore, 560066
          </p>
        </div>

        <div className="flex flex-row mb-1 items-start">
          <p className="text-4xl font-bold ml-2 mr-2 items-end text-end top-0 -mt-[1.2rem]">
            .
          </p>
          <p>
            Clients can write to the Compliance Officer at{" "}
            <a
              href="<EMAIL>"
              target="_blank"
              className="text-[#800020]"
            >
              <EMAIL>
            </a>{" "}
            if the Investor does not receive a response within 10 business days
            of writing to the Client Servicing Team. The client can expect a
            reply within 10 business days of approaching the Compliance Officer.
          </p>
        </div>

        <div className="flex flex-row mb-1 items-start">
          <p className="text-4xl font-bold ml-2 mr-2 items-end text-end top-0 -mt-[1.2rem]">
            .
          </p>
          <div>
            In case you are not satisfied with our response you can lodge your
            grievance with SEBI at{" "}
            <a
              href="https://scores.sebi.gov.in/scores-home"
              target="_blank"
              className="text-[#800020]"
            >
              https://scores.sebi.gov.in/scores-home{" "}
            </a>
              or you may also write to any of the offices of SEBI. SCORES may be
            accessed thorough SCORES mobile application as well, same can be
            downloaded from below link: 
            <a
              href="https://play.google.com/store/apps/details?id=com.sebi&pli=1"
              target="_blank"
              className="text-[#800020] break-all"
            >
              https://play.google.com/store/apps/details?id=com.sebi&pli=1
            </a>
          </div>
        </div>
        {/* );
        })} */}

        {/* Table Information */}

        {/* Table 1 */}

        {govData1.map((item, index) => {
          return (
            <div className="mt-12">
              <p className="text-xl font-semibold mb-5">
                {item.heading}{" "}
                {index == 0 && (
                  <span className=" underline">30th April 2025</span>
                )}
              </p>
              <div className=" overflow-x-scroll w-full md:max-w-[60vw] container">
                <Table
                  removeWrapper
                  aria-label="Example static collection table"
                >
                  <TableHeader>
                    <TableColumn className=" uppercase text-black text-sm">
                      SR.no
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Received from
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm ">
                      Pending at the end of last month
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Received
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Resolved*
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Total Pending#
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm ">
                      Pending complaints more than 3 months
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Average Resolution time^ (in days)
                    </TableColumn>
                  </TableHeader>
                  <TableBody>
                    {item.tableData.map((item, index) => {
                      return (
                        <TableRow key={index} className=" border-b-1 py-10">
                          <TableCell
                            colSpan={item.srNo == "Grand Total" ? 2 : 1}
                            className={
                              item.srNo == "Grand Total"
                                ? "text-center font-semibold "
                                : ""
                            }
                          >
                            {item.srNo}
                          </TableCell>
                          <TableCell
                            className={
                              item.srNo == "Grand Total"
                                ? "hidden"
                                : "text-nowrap"
                            }
                          >
                            {item.receivedFrom}
                          </TableCell>
                          <TableCell>
                            {item.pendingComplaintsOver3Months}
                          </TableCell>
                          <TableCell>{item.received}</TableCell>
                          <TableCell>{item.resolved}</TableCell>
                          <TableCell>{item.totalPending}</TableCell>
                          <TableCell>{item.pendingLastMonth}</TableCell>
                          <TableCell>
                            {item.averageResolutionTimeDays}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              <p className="mt-3">{item.subheading}</p>
            </div>
          );
        })}

        {/* Table 2 */}

        {govData2.map((item, index) => {
          return (
            <div className="mt-12">
              <p className="text-xl font-semibold mb-5">{item.heading}</p>
              <div className=" overflow-x-scroll w-full md:max-w-[60vw] container">
                <Table
                  removeWrapper
                  aria-label="Example static collection table"
                >
                  <TableHeader>
                    <TableColumn className=" uppercase text-black text-sm">
                      SR.no
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Month
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm ">
                      Carried forward from previous month
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Received
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Resolved*
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Total Pending#
                    </TableColumn>
                  </TableHeader>
                  <TableBody>
                    {item.tableData.map((item, index) => {
                      return (
                        <TableRow key={index} className=" border-b-1 py-10">
                          <TableCell
                            colSpan={item.srNo == "Grand Total" ? 2 : 1}
                            className={
                              item.srNo == "Grand Total"
                                ? "text-center font-semibold "
                                : ""
                            }
                          >
                            {item.srNo}
                          </TableCell>
                          <TableCell
                            className={
                              item.srNo == "Grand Total" ? "hidden" : ""
                            }
                          >
                            {item.month}
                          </TableCell>
                          <TableCell>{item.carriedForward}</TableCell>
                          <TableCell>{item.received}</TableCell>
                          <TableCell>{item.resolved}</TableCell>
                          <TableCell>{item.totalPending}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              <p className="mt-3">{item.subheading}</p>
            </div>
          );
        })}

        {/* Table 3 */}

        {govData3.map((item, index) => {
          return (
            <div className="mt-12">
              <p className="text-xl font-semibold mb-5">{item.heading}</p>
              <div className=" overflow-x-scroll w-full md:max-w-[60vw] container">
                <Table
                  removeWrapper
                  aria-label="Example static collection table"
                >
                  <TableHeader>
                    <TableColumn className=" uppercase text-black text-sm">
                      SR.no
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      year
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm ">
                      Carried forward from previous year
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Received
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Resolved*
                    </TableColumn>
                    <TableColumn className=" uppercase text-black text-sm">
                      Total Pending#
                    </TableColumn>
                  </TableHeader>
                  <TableBody>
                    {item.tableData.map((item, index) => {
                      return (
                        <TableRow key={index} className=" border-b-1 py-10">
                          <TableCell
                            colSpan={item.srNo == "Grand Total" ? 2 : 1}
                            className={
                              item.srNo == "Grand Total"
                                ? "text-center font-semibold "
                                : ""
                            }
                          >
                            {item.srNo}
                          </TableCell>
                          <TableCell
                            className={
                              item.srNo == "Grand Total" ? "hidden" : ""
                            }
                          >
                            {item.year}
                          </TableCell>
                          {/* )} */}

                          <TableCell>{item.carriedForward}</TableCell>
                          <TableCell>{item.received}</TableCell>
                          <TableCell>{item.resolved}</TableCell>
                          <TableCell>{item.totalPending}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              <p className="mt-3">{item.subheading}</p>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default Complaints;
