"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import WorldMap from "./mapImg";
import AmChart from "./amChartMap";
import { useEffect, useState } from "react";

const LandingHero = () => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Cleanup the event listener on component unmount
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <div className="grid grid-cols-2 max-md:grid-cols-1  max-md:py-16 items-center h-screen md:-mt-14">
        <div>
          <p
            className="text-[3.5rem] font-[600] max-lg:text-[2rem] max-md:text-[2rem] 
          tracking-[-1px] md:pr-12
          leading-[60px] max-md:leading-[45px] max-md:text-center"
          >
            Human Ingenuity, Quantitative Excellence
          </p>
          <p className="text-xl text-secondary mt-3 mb-5 font-[500] max-md:text-center">
            Redefining Investment with AI/ML
          </p>
          <div className="flex flex-row gap-3 max-md:justify-center max-md:items-center  max-md:flex-col mt-6">
            {/* <Link href="/contactUs">
              <Button className="bg-primary text-white rounded-xl h-11 font-bold">
                Contact Us
                <ArrowRight size={32} strokeWidth="3px" />
              </Button>
            </Link> */}
            <Link href="/investmentProcess" className="-mt-4 h-11 max-md:mb-4">
              <Button
                className=" btn-border hover:bg-primary hover:text-white min-h-11"
                variant="outline"
              >
                Learn More
              </Button>
            </Link>
            <Link href="/contactUs?scroll=true">
              <Button className="bg-primary text-white rounded-xl h-11 font-bold">
                {/* Contact Us */}
                Request An Appointment
                <ArrowRight size={32} strokeWidth="3px" />
              </Button>
            </Link>
          </div>
        </div>
        <div className="row justify-end max-md:justify-start max-md:mt-8">
          {/* <img
            src="/assets/hero.svg"
            alt=""
            className="w-[90%] justify-end flex flex-row"
          /> */}
          {/* <WorldMap /> */}
          <AmChart />
        </div>
      </div>
      <div className="relative">
        <a
          href="#read-more"
          className={`more animated fadeInUp max-md:hidden ${
            isVisible ? "opacity-100" : "opacity-0"
          }`}
          data-wow-delay=".5s"
        ></a>
      </div>
    </>
  );
};

export default LandingHero;
