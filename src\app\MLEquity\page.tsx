"use client";

import ConflictofInterestPolicy from "@/screens/word/pdf";

import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

const PdfViewer = dynamic(() => import("@/components/PdfViewer"), {
  ssr: false, // Prevents hydration issues
});
export default function ConflictofInterestPolicyPage() {
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  if (!mounted) return <div>Loading PDF Viewer...</div>;
  return (
    <div>
      {/* <ConflictofInterestPolicy url="https://res.cloudinary.com/dbz8cdpis/raw/upload/v1738137750/arthalpha/Product_Deck_ArthAlpha_ygcbkn.docx" /> */}
      <div className="p-4">
        <PdfViewer fileUrl="/pdf/Product_Deck_ArthAlpha.pdf" />
      </div>
    </div>
  );
}
