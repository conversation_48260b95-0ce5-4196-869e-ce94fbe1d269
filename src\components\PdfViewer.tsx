"use client"; // Ensure this component runs only in the browser

import { Viewer } from "@react-pdf-viewer/core";
import * as pdfjs from "pdfjs-dist";
import "@react-pdf-viewer/core/lib/styles/index.css";

// Ensure worker is set only in the browser (fixes SSR issue)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

interface PdfViewerProps {
  fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
  return (
    <div className="w-full">
      <Viewer fileUrl={fileUrl} />
    </div>
  );
};

export default PdfViewer;
