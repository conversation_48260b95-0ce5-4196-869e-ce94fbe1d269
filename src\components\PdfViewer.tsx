"use client"; // Ensure this component runs only in the browser

import React, { useState } from "react";
import { <PERSON><PERSON>, Worker } from "@react-pdf-viewer/core";
import { pageNavigationPlugin } from "@react-pdf-viewer/page-navigation";
import { toolbarPlugin } from "@react-pdf-viewer/toolbar";
import * as pdfjs from "pdfjs-dist";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Import styles
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/page-navigation/lib/styles/index.css";
import "@react-pdf-viewer/toolbar/lib/styles/index.css";

// Ensure worker is set only in the browser (fixes SSR issue)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

interface PdfViewerProps {
  fileUrl: string;
  carouselStyle?: boolean; // New prop to enable carousel-style navigation
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  fileUrl,
  carouselStyle = false,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [numPages, setNumPages] = useState(0);

  // Create page navigation plugin
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const { jumpToPage } = pageNavigationPluginInstance;

  // Create toolbar plugin (hidden when carousel style is enabled)
  const toolbarPluginInstance = toolbarPlugin();

  const handleDocumentLoad = (e: any) => {
    setNumPages(e.doc.numPages);
  };

  const handlePageChange = (e: any) => {
    setCurrentPage(e.currentPage);
  };

  const goToPreviousPage = () => {
    if (currentPage > 0) {
      jumpToPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages - 1) {
      jumpToPage(currentPage + 1);
    }
  };

  if (carouselStyle) {
    return (
      <div className="relative w-full min-h-screen bg-gray-50">
        {/* Carousel-style PDF Viewer */}
        <div className="relative">
          <Worker
            workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
          >
            <div className="pdf-viewer-container" style={{ height: "80vh" }}>
              <Viewer
                fileUrl={fileUrl}
                plugins={[pageNavigationPluginInstance]}
                onDocumentLoad={handleDocumentLoad}
                onPageChange={handlePageChange}
              />
            </div>
          </Worker>

          {/* Carousel Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className={cn(
              "absolute left-4 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-white hover:bg-gray-100 shadow-lg z-10",
              currentPage === 0 && "opacity-50 cursor-not-allowed"
            )}
            disabled={currentPage === 0}
            onClick={goToPreviousPage}
          >
            <ArrowLeft className="h-6 w-6" />
            <span className="sr-only">Previous page</span>
          </Button>

          <Button
            variant="outline"
            size="icon"
            className={cn(
              "absolute right-4 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-white hover:bg-gray-100 shadow-lg z-10",
              currentPage === numPages - 1 && "opacity-50 cursor-not-allowed"
            )}
            disabled={currentPage === numPages - 1}
            onClick={goToNextPage}
          >
            <ArrowRight className="h-6 w-6" />
            <span className="sr-only">Next page</span>
          </Button>

          {/* Page Indicator */}
          {numPages > 0 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm font-medium z-10">
              {currentPage + 1} of {numPages}
            </div>
          )}
        </div>

        {/* Page Dots Indicator (like carousel) */}
        {numPages > 0 &&
          numPages <= 20 && ( // Only show dots for reasonable number of pages
            <div className="flex justify-center mt-4 space-x-2">
              {Array.from({ length: numPages }, (_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-200",
                    index === currentPage
                      ? "bg-primary w-6"
                      : "bg-gray-300 hover:bg-gray-400"
                  )}
                  onClick={() => jumpToPage(index)}
                  aria-label={`Go to page ${index + 1}`}
                />
              ))}
            </div>
          )}
      </div>
    );
  }

  // Default PDF viewer (original behavior)
  return (
    <div className="w-full min-h-screen">
      <Worker
        workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
      >
        <Viewer
          fileUrl={fileUrl}
          plugins={[toolbarPluginInstance, pageNavigationPluginInstance]}
        />
      </Worker>
    </div>
  );
};

export default PdfViewer;
