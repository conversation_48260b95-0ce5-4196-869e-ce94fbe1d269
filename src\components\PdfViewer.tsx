"use client"; // Ensure this component runs only in the browser

import React, { useState, useEffect } from "react";
import {
  <PERSON>er,
  Worker,
  SpecialZoomLevel,
  ScrollMode,
  ViewMode,
} from "@react-pdf-viewer/core";
import { pageNavigationPlugin } from "@react-pdf-viewer/page-navigation";
import { toolbarPlugin } from "@react-pdf-viewer/toolbar";
import * as pdfjs from "pdfjs-dist";
import {
  ArrowLeft,
  ArrowRight,
  Maximize,
  Minimize,
  Play,
  Pause,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Import styles
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/page-navigation/lib/styles/index.css";
import "@react-pdf-viewer/toolbar/lib/styles/index.css";

// Ensure worker is set only in the browser (fixes SSR issue)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

// Hook to detect mobile devices
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint in Tailwind
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  return isMobile;
};

interface PdfViewerProps {
  fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [numPages, setNumPages] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isPresentationMode, setIsPresentationMode] = useState(false);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const isMobile = useIsMobile();

  // Create page navigation plugin
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const { jumpToPage } = pageNavigationPluginInstance;

  // Create toolbar plugin (hidden when carousel style is enabled on mobile)
  const toolbarPluginInstance = toolbarPlugin();

  const handleDocumentLoad = (e: any) => {
    setNumPages(e.doc.numPages);
  };

  const handlePageChange = (e: any) => {
    setCurrentPage(e.currentPage);
  };

  const goToPreviousPage = () => {
    if (currentPage > 0 && !isTransitioning) {
      setIsTransitioning(true);
      jumpToPage(currentPage - 1);
      // Reset transition state after animation
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages - 1 && !isTransitioning) {
      setIsTransitioning(true);
      jumpToPage(currentPage + 1);
      // Reset transition state after animation
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  // Presentation mode auto-play
  useEffect(() => {
    if (!isAutoPlay || isPresentationMode) return;

    const interval = setInterval(() => {
      if (currentPage < numPages - 1) {
        goToNextPage();
      } else {
        setIsAutoPlay(false); // Stop at the end
      }
    }, 3000); // 3 seconds per slide

    return () => clearInterval(interval);
  }, [isAutoPlay, currentPage, numPages, isPresentationMode]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        goToPreviousPage();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        goToNextPage();
      } else if (event.key === "Escape" && isPresentationMode) {
        setIsPresentationMode(false);
      } else if (
        event.key === "F11" ||
        (event.key === "f" && !isPresentationMode)
      ) {
        event.preventDefault();
        setIsPresentationMode(!isPresentationMode);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [currentPage, numPages, isPresentationMode]);

  // Toggle presentation mode
  const togglePresentationMode = () => {
    setIsPresentationMode(!isPresentationMode);
    if (isAutoPlay) setIsAutoPlay(false);
  };

  // Toggle auto-play
  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  // Mobile carousel-style view or presentation mode
  if (isMobile || isPresentationMode) {
    return (
      <div
        className={cn(
          "relative w-full",
          isPresentationMode
            ? "fixed inset-0 z-50 bg-black"
            : "min-h-screen bg-gray-50"
        )}
      >
        {/* Mobile Carousel-style PDF Viewer */}
        <div className="relative">
          <Worker
            workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
          >
            <div
              className={cn(
                "pdf-viewer-container overflow-hidden relative",
                isTransitioning && "opacity-90"
              )}
              style={{
                height: isPresentationMode ? "100vh" : "calc(100vh - 220px)",
                // Ensure single page display
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                transition: "opacity 0.2s ease-in-out",
              }}
              onTouchStart={(e) => {
                const touch = e.touches[0];
                const rect = e.currentTarget.getBoundingClientRect();

                // Only handle if touch is within the PDF container bounds
                if (
                  touch.clientX >= rect.left &&
                  touch.clientX <= rect.right &&
                  touch.clientY >= rect.top &&
                  touch.clientY <= rect.bottom
                ) {
                  e.currentTarget.dataset.startX = touch.clientX.toString();
                  e.currentTarget.dataset.startY = touch.clientY.toString();
                  e.currentTarget.dataset.isValidTouch = "true";
                } else {
                  e.currentTarget.dataset.isValidTouch = "false";
                }
              }}
              onTouchMove={(e) => {
                // Only handle if this was a valid touch start within our container
                if (e.currentTarget.dataset.isValidTouch !== "true") {
                  return;
                }

                const startX = parseFloat(
                  e.currentTarget.dataset.startX || "0"
                );
                const startY = parseFloat(
                  e.currentTarget.dataset.startY || "0"
                );
                const currentX = e.touches[0].clientX;
                const currentY = e.touches[0].clientY;

                const diffX = Math.abs(startX - currentX);
                const diffY = Math.abs(startY - currentY);

                // Only prevent default if it's clearly a horizontal swipe
                if (diffX > diffY && diffX > 20) {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              onTouchEnd={(e) => {
                // Only handle if this was a valid touch start within our container
                if (e.currentTarget.dataset.isValidTouch !== "true") {
                  return;
                }

                e.stopPropagation();

                const startX = parseFloat(
                  e.currentTarget.dataset.startX || "0"
                );
                const startY = parseFloat(
                  e.currentTarget.dataset.startY || "0"
                );
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;

                const diffX = startX - endX;
                const diffY = startY - endY;

                // Reset the valid touch flag
                e.currentTarget.dataset.isValidTouch = "false";

                // Only trigger if horizontal swipe is more significant than vertical
                // and swipe distance is sufficient
                if (
                  Math.abs(diffX) > Math.abs(diffY) &&
                  Math.abs(diffX) > 120 &&
                  !isTransitioning
                ) {
                  if (diffX > 0 && currentPage < numPages - 1) {
                    // Swiped left - go to next page
                    goToNextPage();
                  } else if (diffX < 0 && currentPage > 0) {
                    // Swiped right - go to previous page
                    goToPreviousPage();
                  }
                }
              }}
            >
              <Viewer
                fileUrl={fileUrl}
                plugins={[pageNavigationPluginInstance]}
                onDocumentLoad={handleDocumentLoad}
                onPageChange={handlePageChange}
                defaultScale={SpecialZoomLevel.PageWidth}
                scrollMode={ScrollMode.Page} // Single page mode
                viewMode={ViewMode.SinglePage} // Single page view
              />
            </div>
          </Worker>

          {/* Mobile Navigation Buttons - styled like your existing carousel */}
          <Button
            variant="outline"
            size="icon"
            className={cn(
              "absolute left-2 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white hover:bg-gray-200 text-primary shadow-lg z-20",
              currentPage === 0 && "opacity-50 cursor-not-allowed"
            )}
            disabled={currentPage === 0}
            onClick={goToPreviousPage}
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Previous page</span>
          </Button>

          <Button
            variant="outline"
            size="icon"
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white hover:bg-gray-200 text-primary shadow-lg z-20",
              currentPage === numPages - 1 && "opacity-50 cursor-not-allowed"
            )}
            disabled={currentPage === numPages - 1}
            onClick={goToNextPage}
          >
            <ArrowRight className="h-5 w-5" />
            <span className="sr-only">Next page</span>
          </Button>

          {/* Mobile Page Indicator */}
          {numPages > 0 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/80 text-white px-3 py-1 rounded-full text-sm font-medium z-20">
              {currentPage + 1} / {numPages}
            </div>
          )}

          {/* Presentation Mode Controls */}
          <div className="absolute top-4 right-4 flex flex-col gap-2 z-20">
            {isPresentationMode ? (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md"
                  onClick={togglePresentationMode}
                  title="Exit Presentation Mode (ESC)"
                >
                  <Minimize className="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md",
                    isAutoPlay && "bg-primary text-white"
                  )}
                  onClick={toggleAutoPlay}
                  title={isAutoPlay ? "Stop Auto-play" : "Start Auto-play"}
                >
                  {isAutoPlay ? (
                    <Pause className="h-5 w-5" />
                  ) : (
                    <Play className="h-5 w-5" />
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md"
                  onClick={togglePresentationMode}
                  title="Enter Presentation Mode (F)"
                >
                  <Maximize className="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md",
                    isAutoPlay && "bg-primary text-white"
                  )}
                  onClick={toggleAutoPlay}
                  title={isAutoPlay ? "Stop Auto-play" : "Start Auto-play"}
                >
                  {isAutoPlay ? (
                    <Pause className="h-5 w-5" />
                  ) : (
                    <Play className="h-5 w-5" />
                  )}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile Page Dots Indicator (like your existing carousel) */}
        {numPages > 0 && numPages <= 10 && (
          <div className="flex justify-center mt-4 px-4 space-x-1">
            {Array.from({ length: numPages }, (_, index) => (
              <button
                key={index}
                className={cn(
                  "h-2 rounded-full transition-all duration-300",
                  index === currentPage
                    ? "bg-primary w-6"
                    : "bg-gray-300 w-2 hover:bg-gray-400"
                )}
                onClick={() => jumpToPage(index)}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Mobile Page Navigation for many pages */}
        {numPages > 10 && (
          <div className="flex justify-center mt-4 px-4">
            <div className="flex items-center gap-3 bg-white rounded-full px-4 py-2 shadow-md">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium min-w-[60px] text-center">
                {currentPage + 1} / {numPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === numPages - 1}
                className="h-8 w-8 p-0"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default PDF viewer (desktop behavior with toolbar)
  return (
    <div className="w-full min-h-screen">
      <Worker
        workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
      >
        <Viewer fileUrl={fileUrl} />
      </Worker>
    </div>
  );
};

export default PdfViewer;
