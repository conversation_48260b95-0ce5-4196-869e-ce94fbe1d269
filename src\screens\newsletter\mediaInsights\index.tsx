"use client";
import { useState } from "react";
import MediaCard from "./mediaCard";
import { Button } from "@/components/ui/button";

const data = ["All", "News", "Blog", "Articles", "Newsletters", "Quotes"];

const BlogsMediaInsights = () => {
  const [mediaType, setMediaType] = useState("All");
  const [isActive, setIsActive] = useState(false);
  return (
    <>
      <div>
        {/* <div className="gap-3 space-x-2 space-y-2 mt-6">
          {data.map((item, index) => (
            <Button
              key={index}
              className={
                mediaType == item
                  ? " text-white rounded-full fill-primary font-bold"
                  : " rounded-full border-primary bg-none text-primary hover:bg-primary hover:text-white"
              }
              variant={mediaType == item ? "default" : "outline"}
              onClick={() => setMediaType(item)}
            >
              {item}
            </Button>
          ))}
        </div> */}
        <MediaCard mediaType={mediaType} />
      </div>
    </>
  );
};

export default BlogsMediaInsights;
