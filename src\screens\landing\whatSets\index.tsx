// import Image from "next/image";
// import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

// export default function WhatSetsUsApart() {
//   return (
//     <div className=" mx-auto mt-32 max-md:mt-12  py-8 bg-[#F8F8F8] max-md:py-4">
//       <div className=" container mx-auto px-32  max-md:px-4 max-lg:px-4">
//         <h2 className="heading text-center mb-8 max-md:mb-6">
//           What Sets Us Apart
//         </h2>
//         <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
//           {/* First Column: 2 Items */}
//           <div className="flex flex-col gap-4 h-full max-md:col-span-2">
//             <Card className="card bg-white flex-1 p-4">
//               <CardHeader className="flex items-center gap-4">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723122/arthalpha/sets1_qynixr.svg"
//                   alt="Investor Insights"
//                   width={54}
//                   height={54}
//                   className="w-16 h-16"
//                 />
//               </CardHeader>
//               <CardContent>
//                 <p className="text-xl font-bold text-primary text-center  mb-2">
//                   Investor Insights
//                 </p>
//                 <p className="text-gray-600 text-center">
//                   Empowering investors with insights that turn data into
//                   opportunities.
//                 </p>
//               </CardContent>
//             </Card>

//             <Card className="card bg-white flex-1">
//               <CardHeader className="flex items-center gap-4">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723121/arthalpha/sets2_kvk6x5.svg"
//                   alt="Continuous Innovation"
//                   width={48}
//                   height={48}
//                   className="w-20 h-20"
//                 />
//               </CardHeader>
//               <CardContent>
//                 <p className="text-xl font-bold text-primary text-center mb-2">
//                   Continuous Innovation
//                 </p>
//                 <p className="text-gray-600 text-center">
//                   Leading portfolio management with AI-driven strategies.
//                 </p>
//               </CardContent>
//             </Card>
//           </div>

//           {/* Second Column: 3 Items */}
//           <div className="flex flex-col gap-4 h-full col-span-2">
//             <Card className="card row max-md:flex-col">
//               <CardHeader className="flex items-start gap-4 ">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734420222/arthalpha/2_dkoiep.svg"
//                   alt="Consistent Growth"
//                   width={48}
//                   height={48}
//                   className="w-20 h-20"
//                 />
//               </CardHeader>
//               <CardContent className="pt-4">
//                 <p className="text-xl font-bold text-primary text-center md:text-start mb-2 ">
//                   Consistent Growth
//                 </p>
//                 <p className="text-gray-600 text-center md:text-start">
//                   Delivering steady returns with our MEQ strategy.
//                 </p>
//               </CardContent>
//             </Card>

//             {/* Center Card */}
//             <Card className="card bg-primary text-white flex-1">
//               <CardHeader className="text-center ">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723121/arthalpha/sets4_bmyr44.svg"
//                   alt="Investor Focus"
//                   width={48}
//                   height={48}
//                   className="mx-auto pb-6 w-28 h-20 border-b"
//                 />
//               </CardHeader>
//               <CardContent>
//                 <p className="text-2xl font-bold text-center mb-2">
//                   Investor Focus
//                 </p>
//                 <p className="text-center">
//                   Our commitment to prioritize investor goals ensures tailored
//                   strategies and transparent partnerships that drive mutual
//                   success.
//                 </p>
//               </CardContent>
//             </Card>

//             <Card className="card row  max-md:flex-col">
//               <CardHeader className="flex items-center gap-4">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723121/arthalpha/sets5_tamxdv.svg"
//                   alt="Risk Management"
//                   width={48}
//                   height={48}
//                   className="w-20 h-20"
//                 />
//               </CardHeader>
//               <CardContent className="pt-2 ">
//                 <p className="text-xl font-bold text-red-800 text-center md:text-start mb-2">
//                   Risk Management
//                 </p>
//                 <p className="text-gray-600 text-center md:text-start">
//                   Mitigate risk and protect portfolios from volatility.
//                 </p>
//               </CardContent>
//             </Card>
//           </div>

//           {/* Third Column: 2 Items */}
//           <div className="flex flex-col gap-4 h-full max-md:col-span-2">
//             <Card className="card bg-white flex-1">
//               <CardHeader className="flex items-center gap-4">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723121/arthalpha/sets6_g0ktj6.svg"
//                   alt="Ethical Stewardship"
//                   width={48}
//                   height={48}
//                   className="w-20 h-20"
//                 />
//               </CardHeader>
//               <CardContent>
//                 <p className="text-xl font-bold text-red-800 text-center mb-2">
//                   Ethical Stewardship
//                 </p>
//                 <p className="text-gray-600 text-center">
//                   Prioritizing sustainable, ethical financial success.
//                 </p>
//               </CardContent>
//             </Card>

//             <Card className="card bg-white flex-1">
//               <CardHeader className="flex items-center gap-4">
//                 <Image
//                   src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733723121/arthalpha/sets7_ac8ewk.svg"
//                   alt="Portfolio Management"
//                   width={48}
//                   height={48}
//                   className="w-20 h-20"
//                 />
//               </CardHeader>
//               <CardContent>
//                 <p className="text-xl font-bold text-red-800 text-center mb-2">
//                   Portfolio Management
//                 </p>
//                 <p className="text-gray-600 text-center">
//                   Personalized Portfolio Management Services (PMS).
//                 </p>
//               </CardContent>
//             </Card>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

"use client";
import { useState } from "react";
import Image from "next/image";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

export default function WhatSetsUsApart() {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="mx-auto mt-32 max-md:mt-12 py-8 bg-[#F8F8F8] max-md:py-4">
      <div className="container mx-auto px-32 max-md:px-4 max-lg:px-4">
        <h2 className="heading text-center mb-8 max-md:mb-6">
          What Sets Us Apart
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* First Column */}
          <div className="flex flex-col gap-4 h-full max-md:col-span-2">
            <div className="group">
              <Card
                className="card bg-white flex-1 p-4 group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-center gap-4">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737621668/arthalpha/img1_ssssb7.svg"
                    alt="Investor Insights"
                    width={54}
                    height={54}
                    className="w-16 h-16 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-bold text-center mb-2">Talent</p>
                  <p className="text-gray-600 text-center group-hover:text-white">
                    Expertise in quantitative research delivering cutting-edge
                    investment solutions.
                  </p>
                </CardContent>
              </Card>
            </div>
            <div className="group">
              <Card
                className="card bg-white flex-1 group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-center gap-4">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737700684/arthalpha/Vectorf_dpwoah.svg"
                    alt="Continuous Innovation"
                    width={48}
                    height={48}
                    className="w-20 h-20 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-bold text-center mb-2">
                    Data Expertise
                  </p>
                  <p className="text-gray-600 text-center group-hover:text-white">
                    Leveraging the depth and breadth of traditional and
                    alternative datasets effectively.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Second Column */}
          <div className="flex flex-col gap-4 h-full col-span-2">
            <div className="group">
              <Card
                className="card row max-md:flex-col group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-start gap-4">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737621666/arthalpha/img3_mqlc25.svg"
                    alt="Consistent Growth"
                    width={48}
                    height={48}
                    className="w-20 h-20 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent className="pt-4">
                  <p className="text-xl font-bold text-center md:text-start mb-2">
                    Ethical Stewardship
                  </p>
                  <p className="text-gray-600 text-center md:text-start group-hover:text-white">
                    Prioritizing integrity and sustainability in every financial
                    strategy we create.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Center Card */}
            <Card
              className={`card flex-1 transition-all duration-300 ${
                isHovered ? "bg-white text-black" : "bg-primary text-white"
              }`}
            >
              <CardHeader className="text-center">
                <Image
                  src={
                    !isHovered
                      ? "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737700684/arthalpha/Group_11108_ld2drz.svg"
                      : "https://res.cloudinary.com/dbz8cdpis/image/upload/v1737621666/arthalpha/img2_qak7gs.svg"
                  }
                  alt="Investor Focus"
                  width={48}
                  height={48}
                  // className="mx-auto pb-6 w-28 h-20 border-b"
                  className={`mx-auto pb-6 w-28 h-20 border-b ${
                    isHovered ? "bg-white text-black" : "bg-primary text-white"
                  }`}
                />
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-center mb-2">
                  Technology
                </p>
                <p className="text-center">
                  Leveraging advanced AI-driven tools for smarter, seamless
                  portfolio management.
                </p>
              </CardContent>
            </Card>

            <div className="group">
              <Card
                className="card row max-md:flex-col group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-center gap-2">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737621666/arthalpha/img5_r77gew.svg"
                    alt="Risk Management"
                    width={80}
                    height={80}
                    className="w-20 h-20 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent className="pt-2">
                  <p className="text-xl font-bold text-center md:text-start mb-2">
                    Behavioral Finance
                  </p>
                  <p className="text-gray-600 text-center md:text-start group-hover:text-white">
                    Integrating behavioral insights seamlessly into our robust
                    MEQ model.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Third Column */}
          <div className="flex flex-col gap-4 h-full max-md:col-span-2">
            <div className="group">
              <Card
                className="card bg-white flex-1 group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-center gap-4">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737700684/arthalpha/sfdsf_tmrff9.svg"
                    alt="Ethical Stewardship"
                    width={48}
                    height={48}
                    className="w-20 h-20 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-bold text-center mb-2">
                    Transparency
                  </p>
                  <p className="text-gray-600 text-center group-hover:text-white">
                    Clear, data-backed insights fostering trust and empowering
                    informed decisions.
                  </p>
                </CardContent>
              </Card>
            </div>
            <div className="group">
              <Card
                className="card bg-white flex-1 group-hover:bg-primary group-hover:text-white"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <CardHeader className="flex items-center gap-4">
                  <Image
                    src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737621666/arthalpha/img7_feifqo.svg"
                    alt="Portfolio Management"
                    width={48}
                    height={48}
                    className="w-20 h-20 transition-all duration-300 group-hover:invert group-hover:brightness-0"
                  />
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-bold text-center mb-2">
                    Seamless Support
                  </p>
                  <p className="text-gray-600 text-center group-hover:text-white">
                    Simplified, seamless access for clients to address any
                    concerns instantly.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
