import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>ed<PERSON> } from "lucide-react";
import Link from "next/link";

const LandingOurTeem = () => {
  return (
    <>
      <div>
        <p className="heading mt-32 max-sm:mt-12 text-center">Meet Our Team</p>
      </div>
      <div className="grid grid-cols-2 gap-6 max-md:grid-cols-1 my-12 max-md:my-6 px-32 max-md:px-0 max-lg:px-12">
        <div>
          <Card
            className="p-0 bg-[#FAF5F5]  rounded-xl border-primary border-[3px]"
            style={{ padding: 0 }}
          >
            <CardContent
              className="relative rounded-2xl group"
              style={{ padding: 0 }}
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733724849/arthalpha/eri_yzlpow.png"
                alt=""
                className="h-[500px] min-h-[500px] w-full object-cover rounded-lg"
              />
              <div className="row justify-center ">
                <div className=" absolute bottom-3 bg-primary  bg-opacity-60 backdrop-blur-md  w-[96%] px-4 rounded-lg py-2 bg-transparent-[50%]">
                  <p className="text-lg text-white font-bold">Rohit Beri</p>
                  <p className="text-base text-white">Co-Founder</p>
                  <p className="text-white mt-2 hidden group-hover:block duration-500">
                    Rohit is a CA, a Stanford GSB graduate, and a Data Scientist
                    with a stellar track record as an investment manager. He
                    co-founded Roaring Numbers, a U.S. hedge fund, and Riemann
                    Capital, a Singapore-based wealth advisory firm. His
                    leadership and expertise have contributed to the success of
                    True Beacon, ICICI Bank, Citibank, Crédit Agricole, and ANZ
                  </p>
                </div>
              </div>
              <div className=" absolute top-2 right-2 ">
                <Link
                  href="https://www.linkedin.com/in/rohit-beri/"
                  target="_blank"
                >
                  <img src="/assets/in.svg" alt="" className="w-6 h-6" />
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card
            className="p-0 bg-[#FAF5F5]  border-primary border-[3px]"
            style={{ padding: 0, borderRadius: "10px" }}
          >
            <CardContent className="relative group" style={{ padding: 0 }}>
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1733724849/arthalpha/jha_rsvkej.png"
                alt=""
                className="h-[500px] min-h-[500px] w-full object-cover rounded-lg"
              />
              <div className="row justify-center">
                <div className=" absolute bottom-3 bg-primary  bg-opacity-60 backdrop-blur-md  w-[96%] px-4 rounded-lg py-2">
                  <p className="text-lg text-white font-bold">
                    Rohit Kumar Jha
                  </p>
                  <p className="text-base text-white">Co-Founder</p>
                  <p className="text-white mt-2 hidden group-hover:block duration-500">
                    Rohit, an IIT Kanpur CS graduate, transitioned from being a
                    top hedge fund manager to an entrepreneur. He spearheaded a
                    highly profitable trading unit at WorldQuant and became one
                    of Tower Research Capital’s youngest portfolio managers.
                    Today, he provides expert guidance on quantitative
                    strategies and risk management
                  </p>
                </div>
              </div>
              <div className=" absolute top-2 right-2 ">
                <Link
                  href="https://www.linkedin.com/in/rohitkjha/"
                  target="_blank"
                >
                  <img src="/assets/in.svg" alt="" className="w-6 h-6" />
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <div>
        <p className="text-3xl font-semibold max-sm:mt-12 text-center">
          Investors & Advisors
        </p>
      </div>
      <div className="row justify-center mt-0 max-md:-mt-4 ">
        <div className="flex justify-center">
          <div className="grid -md:grid-cols-1 md:grid-cols-3 text-center justify-center ">
            <div className="my-10 -md:order-2">
              <div className="cardInvest">
                <div className="card__image">
                  <img
                    src="/assets/team/Aditi Kothari.jpg"
                    alt="Aditi Kothari"
                  />
                </div>

                <div className="card__content">
                  <span className="card__title">Aditi Kothari</span>
                  <span className="font-medium text-primary border-b-2 border-primary mb-1">
                    Investor
                  </span>
                  <span className="font-medium text-primary">
                    Chairperson - DSP Mutual Fund
                  </span>

                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button>LinkedIN</Button> */}
                </div>
              </div>
            </div>
            <div className="my-10 max-md:my-0 -md:order-1">
              <div className="cardInvest ">
                <div className="card__image">
                  <img
                    src="/assets/team/Kalpen Parekh.jpg"
                    alt="Kalpen Parekh"
                  />
                </div>

                <div className="card__content">
                  <span className="card__title">Kalpen Parekh</span>
                  <span className="font-medium text-primary  border-b-2 border-primary mb-1">
                    Board Observer
                  </span>
                  <span className="font-medium text-primary">
                    CEO - DSP Mutual Fund
                  </span>
                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button>LinkedIN</Button> */}
                </div>
              </div>
            </div>
            <div className="my-10 -md:order-3 ">
              <div className="cardInvest ">
                <div className="card__image">
                  <img src="/assets/team/Kunal Bajaj.jpg" alt="Kunal Bajaj" />
                </div>

                <div className="card__content">
                  <span className="card__title">Kunal Bajaj</span>
                  <span className="font-medium text-primary  border-b-2 border-primary mb-1">
                    Board Advisor
                  </span>
                  <span className="font-medium text-primary">
                    MD - Pulsar Capital
                  </span>
                  {/* <p className="card__text">
                    Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                    Sit veritatis labore provident non tempora odio est sunt,
                    ipsum
                  </p>
                  <Button color="primary">
                    <img
                      src="../assets/icons/linkedin-logo.svg"
                      width={25}
                      style={{ filter: "brightness(0) invert(1)" }}
                    ></img>
                  </Button> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row justify-center mt-0 max-md:-mt-4 ">
        <Link href="/mastermind">
          <Button
            className="btn-border hover:bg-primary hover:text-white "
            variant="outline"
          >
            Discover Our Team
          </Button>
        </Link>
      </div>
    </>
  );
};

export default LandingOurTeem;
