"use client";
interface TopBannerProps {
  close: boolean;
  setClose: (value: boolean) => void;
}

const TopBanner: React.FC<TopBannerProps> = ({ close, setClose }) => {
  return (
    <div
      className={
        close
          ? "hidden"
          : "block bg-primary text-center py-3 text-white row justify-between px-3"
      }
    >
      <div className="opacity-0">h</div>
      <a
        href="https://inc42.com/buzz/arthalpha-nets-2-mn-to-boost-its-ai-powered-research-tools/"
        target="_blank"
        rel="noopener noreferrer"
      >
        ✨ ArthAlpha secures $2Mn+ in funding led by DSP Asset Managers Private
        Limited and participation from notable angels. 🚀
      </a>
      <div
        onClick={() => setClose(true)}
        className="cursor-pointer px-2 hover:bg-white hover:text-primary rounded-full"
      >
        X
      </div>
    </div>
  );
};

export default TopBanner;
