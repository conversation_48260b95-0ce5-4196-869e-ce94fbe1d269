{"name": "arthalpha", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@amcharts/amcharts5": "^5.10.9", "@amcharts/amcharts5-geodata": "^5.1.4", "@nextui-org/dropdown": "^2.3.4", "@nextui-org/input": "^2.4.6", "@nextui-org/modal": "^2.2.5", "@nextui-org/navbar": "^2.2.8", "@nextui-org/react": "^2.6.11", "@nextui-org/table": "^2.2.6", "@nextui-org/tabs": "^2.2.5", "@nextui-org/tooltip": "^2.2.5", "@radix-ui/react-slot": "^1.1.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.13.1", "gsap": "^3.12.5", "lucide-react": "^0.468.0", "luxon": "^3.5.0", "next": "15.0.4", "pdfjs-dist": "3.9.179", "react": "^19.0.0", "react-dom": "^19.0.0", "react-phone-input-2": "^2.15.1", "react-toastify": "^11.0.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@types/luxon": "^3.4.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}