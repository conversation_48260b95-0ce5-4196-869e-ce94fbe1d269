import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Mail } from "lucide-react";
import Link from "next/link";

const GetInTouch = () => {
  return (
    <>
      <div className="mt-32 max-md:mt-20">
        <Card
          className="p-0 bg-primary text-center border-none rounded-2xl"
          style={{ padding: 0 }}
        >
          <CardContent
          // className="flex flex-col justify-center items-center "
          //   style={{ padding: 0 }}
          >
            <div className="row justify-center">
              <div>
                <p className="text-center pt-6 text-4xl max-md:-2xl font-bold text-white max-md:px-10">
                  Get in touch with us
                </p>
                <p className="text-white mt-3 mb-4 ">
                  We're here to assist you
                </p>
                <div className="row gap-3 justify-center max-md:flex-col">
                  {/* <Link href="https://cal.com/arthalpha" target="_blank">
                    <Button className="text-black bg-white hover:bg-white hover:text-black rounded-xl font-bold h-11 w-[192px] min-w-[192px]">
            
                      Schedule a Call
                    </Button>
                  </Link> */}
                  <Link href="/contactUs?scroll=true">
                    <Button
                      variant="outline"
                      className="text-white bg-transparent rounded-xl font-bold h-11 border-2 w-[192px] min-w-[192px]"
                    >
                      {/* Contact Us */}
                      Request An Appointment
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default GetInTouch;
