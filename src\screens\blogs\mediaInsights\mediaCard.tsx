"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
} from "@nextui-org/modal";
import { link } from "fs";
import Link from "next/link";
import { useState } from "react";

const data = [
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733726518/arthalpha/image3_wjxyvq.svg",
    heading:
      "Where will the Indian Rupee head after a fresh low against the US Dollar?",
    description:
      "“<PERSON>’s administration previously promoted pro-India policies that strengthened trade and investment ties, and this established rapport could foster more favourable trade terms, boost bilateral investments, and potentially stabilize foreign inflows into India. Such an alignment would benefit both markets, as stronger economic cooperation could attract investment, drive trade, and support broader financial stability.”",
    auther: "- Rohit <PERSON><PERSON>",
    type: "News",
    link: "https://www.theweek.in/news/biz-tech/2024/11/08/inr-vs-usd-indian-rupee-depreciation.html",
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733726519/arthalpha/image_zp5m9b.svg",
    heading:
      "Saving for your child’s education abroad? Key advice from financial experts",
    description:
      "“Planning for your child's education abroad involves careful budgeting and investment strategies. With rising costs due to inflation and currency fluctuations, experts recommend starting early”",
    auther: "- Rohit Beri",
    type: "Article",
    link: "https://www.livemint.com/money/personal-finance/saving-for-your-child-s-education-abroad-key-advice-from-financial-experts-mutual-funds-investment-11732601693443.html",
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734528429/arthalpha/new_hp5xny.svg",
    heading:
      "New pension scheme for minors: Should you invest in NPS Vatsalya?",
    description:
      "“The NPS Vatsalya, in my opinion, is not an attractive investment choice. The 75% cap on equity investments is too conservative, especially considering the long lock-in period. Additionally, the restrictions on withdrawals for life events like education, marriage, and home purchases make it less appealing as a savings tool for such milestones. However, it can be a solid option for those looking for a structured, secure retirement plan for their special-needs children or for individuals wanting to instill disciplined savings early on for retirement. It offers the dual benefits of tax efficiency and a steady pension payout.”",
    auther: "- Rohit Kumar Jha",
    type: "News",
    link: "https://www.livemint.com/money/personal-finance/nps-vatsalya-new-pension-scheme-for-minors-should-you-invest-in-it-savings-retirement-national-pension-system-wealth/amp-11731904659651.html",
  },
];

const MediaCard = (props: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItem, setSelectedItem]: any = useState();

  // Open modal and set selected item
  const handleReadMore = (item: any) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };
  return (
    <>
      <div className="grid grid-cols-3 max-lg:grid-cols-2 max-md:grid-cols-1 gap-4 mt-8 mb-12">
        {data.map((item, index) => (
          <Card
            className="p-0 border-none rounded-xl shadow-[4px_12px_40px_6px_rgba(0,0,0,0.09)] h-full"
            key={index}
          >
            <CardContent
              className="relative rounded-2xl flex flex-col h-full "
              style={{ padding: 0 }}
            >
              <div className="relative flex-grow flex flex-col">
                <Link href={item.link} target="_blank">
                  <img
                    src={item.img}
                    alt=""
                    className="h-[200px] w-full object-cover rounded-tl-xl rounded-tr-xl"
                  />
                </Link>

                <div className="absolute top-2 right-2">
                  <Button variant="secondary" className="text-white blur-0 h-8">
                    {props.mediaType === "All" ? item.type : props.mediaType}
                  </Button>
                </div>

                <div className="p-5 -mb-6 flex-grow">
                  <Link href={item.link} target="_blank">
                    <p className="mt-1 text-xl font-bold">{item.heading}</p>
                  </Link>
                </div>
              </div>

              <div className="p-5 mt-auto">
                <div className="bg-[#FAF5F5] rounded-xl p-3">
                  <p className="line-clamp-3">{item.description}</p>
                  <Button
                    className="text-lg font-bold text-primary mt-1 hover:text-primary hover:bg-primary-100"
                    onClick={() => handleReadMore(item)}
                    variant="ghost"
                  >
                    Read More
                  </Button>
                  <p className="text-right">{item.auther}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Next UI Modal */}
        <Modal
          isOpen={isModalOpen}
          onClose={closeModal}
          size="lg"
          className="bg-white max-w-lg -top-12 rounded-lg py-6 custom-modal-backdrop"
          backdrop="opaque"
          classNames={{
            backdrop:
              "bg-gradient-to-t from-zinc-900/50 to-zinc-900/50 backdrop-opacity-20",
          }}
        >
          <ModalContent>
            <ModalHeader className="font-bold text-xl">
              {selectedItem?.heading}
            </ModalHeader>
            <ModalBody>
              <img
                src={selectedItem?.img}
                alt=""
                className="w-[50vw] h-auto rounded"
              />
              <p>{selectedItem?.description}</p>
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                onClick={closeModal}
                className="text-white"
              >
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default MediaCard;
