"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
} from "@nextui-org/modal";
import { link } from "fs";
import Link from "next/link";
import { useState } from "react";

const data = [
  {
    img: "https://res.cloudinary.com/damm9iwho/image/upload/v1739430223/image_lsz4de.svg",
    heading: "DSP leads $2 Mn seed round in ArthAlpha",
    description:
      "“<PERSON>’s administration previously promoted pro-India policies that strengthened trade and investment ties, and this established rapport could foster more favourable trade terms, boost bilateral investments, and potentially stabilize foreign inflows into India. Such an alignment would benefit both markets, as stronger economic cooperation could attract investment, drive trade, and support broader financial stability.”",
    auther: "- Rohit Be<PERSON>",
    type: "News",
    link: "https://entrackr.com/snippets/dsp-leads-2-mn-seed-round-in-arthalpha-8713124",
  },
];

const MediaCard = (props: any) => {
  return (
    <>
      <div className="grid grid-cols-3 max-lg:grid-cols-2 max-md:grid-cols-1 gap-6 mt-8 mb-12">
        {data.map((item, index) => (
          <Card
            className="border-none rounded-xl shadow-[4px_12px_40px_6px_rgba(0,0,0,0.09)] h-full"
            key={index}
          >
            <CardContent
              className="relative rounded-2xl flex flex-col h-full "
              style={{ padding: 0 }}
            >
              <div className="relative flex-grow flex flex-col">
                <Link href={item.link} target="_blank">
                  <img
                    src={item.img}
                    alt=""
                    className="h-[200px] w-full object-cover rounded-tl-xl rounded-tr-xl"
                  />
                </Link>

                <div className="absolute top-2 right-2">
                  <Button variant="secondary" className="text-white blur-0 h-8">
                    {props.mediaType === "All" ? item.type : props.mediaType}
                  </Button>
                </div>

                <div className="p-5 -mb-6 flex-grow">
                  {/* <Link href={item.link} target="_blank"> */}
                  <p className="text-2xl font-bold text-[#314259]">
                    {item.heading}
                  </p>
                  {/* </Link> */}
                </div>
              </div>
              <div className="row justify-between p-5 mb-2 mt-1">
                <div className="">
                  <div className="text-[#9092A3] text-lg font-semibold mb-1">
                    Featured on
                  </div>
                  <img
                    src="https://res.cloudinary.com/damm9iwho/image/upload/v1739430169/image_24_uaucln.svg"
                    alt=""
                  />
                </div>
                <div className="text-[#9092A3] text-lg font-[500] mt-1">
                  Jan 21, 2025
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default MediaCard;
