"use client";

import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const data = [
  {
    heading: "Fundamental",
    heading1: "Evaluation",
    subtitle1:
      "We anchor our investments in solid fundamentals by evaluating companies on intrinsic value and growth potential, focusing on financial health, industry positioning, and long-term resilience. This thorough approach allows us to identify quality investments for our clients.",
    subtitle2: "1",
    hoverBg: "#800020",
    textHover: "#fff",
  },
  {
    heading: "Quantitative",
    heading1: "Finance",
    subtitle1:
      "Employing advanced quantitative models, we eliminate biases and enhance predictive accuracy. Our scientifically-grounded approach identifies market opportunities with precision, aligning investment decisions with calculated, data-backed strategies.",
    subtitle2: "2",
    hoverBg: "#800020",
    textHover: "#fff",
  },
  {
    heading: "Data",
    heading1: "Science",
    subtitle1:
      "With data science at the core, we leverage machine learning and advanced analytics to uncover hidden market trends and insights. This empowers us to adapt our strategies in real-time, ensuring they’re both agile and effective for sustainable growth.",
    subtitle2: "3",
    hoverBg: "#800020",
    textHover: "#fff",
  },
  {
    heading: "Behavioural",
    heading1: "finance",
    subtitle1:
      "Recognizing the impact of psychology on markets, we analyze investor behavior to pinpoint inefficiencies and opportunities. By integrating behavioral insights, we create strategies that capitalize on market anomalies, refining our approach to reflect true asset value.",
    subtitle2: "4",
    hoverBg: "#800020",
    textHover: "#fff",
  },
];

const PillarCardAnimation = () => {
  const isMobile = typeof window !== "undefined" && window.innerWidth <= 768;

  const cardRefs = useRef<HTMLDivElement[]>([]);
  const letterRefs = useRef<Array<HTMLSpanElement[]>>([]);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  useEffect(() => {
    cardRefs.current.forEach((cardAbout, index) => {
      const isEven = index % 2 === 0;
      const direction = isEven ? "-30%" : "30%";

      gsap.fromTo(
        cardAbout,
        {
          x: direction,
          rotation: isEven ? -15 : 15,
          opacity: isMobile ? 0 : 0.2,
        },
        {
          x: 0,
          rotation: 0,
          opacity: 1,
          scrollTrigger: {
            trigger: cardAbout,
            start: isMobile ? "top 40%" : "top 99%",
            end: isMobile ? "bottom 120%" : "bottom 100%",
            scrub: 1.5,
          },
          ease: "power2.out",
        }
      );
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const handleMouseEnter = (index: number) => {
    setHoveredIndex(index);

    if (letterRefs.current[index]) {
      gsap.fromTo(
        letterRefs.current[index],
        { y: "100%", opacity: 1 },
        {
          y: "0%",
          opacity: 1,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
        }
      );
    }
  };

  const handleMouseLeave = (index: number) => {
    setHoveredIndex(null);

    // if (letterRefs.current[index]) {
    //   gsap.fromTo(
    //     letterRefs.current[index],
    //     { y: "-10%", opacity: 1 },
    //     {
    //       y: "0%", // Move back down
    //       opacity: 1, // Fade out
    //       duration: 1,
    //       stagger: 0.2,
    //       ease: "power3.in",
    //     }
    //   );
    // }
  };

  return (
    <>
      <p className="heading text-center  mt-20 max-md:mt-12">Our Pillars</p>
      <div className="container mx-auto px-4 max-md:px-0 pb-20 max-md:pb-12 max-md:pt-6">
        <div className="grid grid-cols-2 gap-6 max-md:gap-4  lg:mt-60 max-lg:grid-cols-1">
          {data.map((item, index) => (
            <div
              key={index}
              className={`bg-[#F2F2F2] rounded-[40px] max-md:rounded-[20px] pt-4 pb-12 px-12 max-md:px-4 max-md:pb-4 w-full h-[350px] max-md:h-full group ${
                index % 2 === 0 ? "lg:-mt-32" : "lg:mt-0"
              } hover:ease-in-out`}
              style={{
                transform: index % 2 === 0 ? "rotate(-15deg)" : "rotate(15deg)",
                backgroundColor:
                  hoveredIndex === index ? item.hoverBg : "#F2F2F2",
                transition: "background-color 0.6s ease",
                color: hoveredIndex === index ? item.textHover : "#000",
              }}
              onMouseEnter={() => handleMouseEnter(index)}
              onMouseLeave={() => handleMouseLeave(index)}
              ref={(el) => {
                if (el) {
                  cardRefs.current[index] = el;
                }
              }}
            >
              <div>
                <p className="text-3xl max-md:text-xl font-semibold flex flex-row items-start justify-start pt-6 text-right max-md:pt-0 uppercase">
                  {item.heading}
                </p>
                <p className="text-3xl max-md:text-xl font-semibold flex flex-row items-start justify-start pt-0 text-right max-md:pt-[2px] uppercase">
                  {item.heading1}
                </p>
                <p className="text-secondary group-hover:text-white mt-3">
                  {item.subtitle1}
                </p>
                <p className=" text-[#D8565E] group-hover:text-white text-3xl max-md:text-2xl font-semibold flex flex-row items-end justify-end pt-8 text-right max-md:pt-6 uppercase">
                  {item.subtitle2}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default PillarCardAnimation;
