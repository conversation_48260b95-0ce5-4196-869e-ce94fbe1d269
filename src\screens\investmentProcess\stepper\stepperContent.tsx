"use client";
import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils"; // For Tailwind's class merging (you can replace this with a simple classNames function)
import CardItem from "./cardItem";
import { ArrowLeft, ArrowRight } from "lucide-react";

const steps = [
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733986369/arthalpha/1_uk0vrt.svg",
    title: "Strategic Hypothesis Development",
    objective: "Craft hypotheses rooted in robust research.",
    points: [
      "Our journey begins by identifying alpha factors—unique insights capable of predicting future stock returns.",
      "Anchored in causal inference, our hypothesis formation avoids statistical coincidences and ensures signals are logically sound.",
      "This process sets the foundation for precision in investment decisions.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733986369/arthalpha/2_reliiu.svg",
    title: "Comprehensive Data Engineering",
    objective: "Transform data into actionable intelligence.",
    points: [
      "Data is the lifeblood of our investment process. ",
      "We source high-quality financial and market data, clean it meticulously to address inconsistencies, and apply advanced feature engineering techniques. ",
      "This ensures every variable contributes meaningfully to the analysis, providing a solid base for our models.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733986369/arthalpha/3_yt3yeb.svg",
    title: "Factor Validation",
    objective: "Authenticate investment signals through rigorous testing.",
    points: [
      "Each identified factor undergoes systematic validation using segmented datasets (training, validation, and testing).",
      "We ensure every alpha factor exhibits statistically significant predictive power across different environments, eliminating risks of overfitting or short-term anomalies.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734592602/arthalpha/multi_ebgidw.svg",
    title: "Multi-Dimensional Signal Refinement",
    objective: "Enhance signal clarity and relevance.",
    points: [
      "Using dimensionality reduction techniques like PCA and auto-encoders, we aggregate correlated factors into “alpha buckets.”",
      "These buckets reduce noise and magnify signal strength, ensuring only the most potent insights influence our strategies.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734592602/arthalpha/adaptive_ryh67t.svg",
    title: "Adaptive Bayesian Integration",
    objective: "Build dynamic, resilient investment models.",
    points: [
      "Through Bayesian modeling, alpha buckets are integrated into a cohesive framework.",
      "This approach adapts to macroeconomic trends, creating strategies that are both robust and responsive to shifting market dynamics.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734592602/arthalpha/Frame_1984078836_maxws4.svg",
    title: "Portfolio Synthesis & Stress Testing",
    objective: "Optimize risk and returns before deployment.",
    points: [
      "The final strategy is constructed by combining multiple validated models.",
      "It undergoes rigorous backtesting, stress simulations, and risk optimization to align performance with investor expectations.",
      "Live paper trading further fine-tunes the strategy before full-scale deployment.",
    ],
  },
  {
    img: "https://res.cloudinary.com/dbz8cdpis/image/upload/v1734592602/arthalpha/14-Monitoring_tkhs7t.svg",
    title: "Continuous Monitoring & Adaptation",
    objective: "Maintain alignment with dynamic markets.",
    points: [
      "Post-deployment, our team continuously monitors macroeconomic conditions, industry trends, and company-specific developments.",
      "This vigilance ensures the strategy evolves with markets while remaining true to its investment thesis.",
    ],
  },
];

export default function StepperContent() {
  const [currentStep, setCurrentStep] = useState(0);
  const scrollPosition = useRef(0);

  const nextStep = () => {
    if (currentStep < steps.length - 1) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  const goToStep = (index: number) => {
    setCurrentStep(index);
  };

  // auto play

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setCurrentStep((prevStep) => (prevStep + 1) % steps.length);
  //   }, 4000);

  //   // Cleanup the interval on component unmount
  //   return () => clearInterval(interval);
  // }, []);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const currentScroll = e.currentTarget.scrollTop;

    if (currentScroll > scrollPosition.current) {
      nextStep(); // Scrolling down
    } else if (currentScroll < scrollPosition.current) {
      prevStep(); // Scrolling up
    }

    scrollPosition.current = currentScroll; // Update scroll position
  };

  return (
    <div
      className=" mx-auto p-6 max-md:p-0 max-md:max-w-[100%] md:max-w-[85%]"
      // onScroll={handleScroll}
    >
      {/* <div className="row gap-3 mb-3 justify-end pr-12 max-lg:pr-0 ">
        <div
          onClick={() => {
            prevStep();
          }}
          className="h-10 w-10 rounded-full border-2 row  justify-center cursor-pointer"
        >
          <ArrowLeft className="text-gray-500" />
        </div>
        <div
          onClick={() => {
            nextStep();
          }}
          className="h-10 w-10 rounded-full border-2 row  justify-center cursor-pointer"
        >
          <ArrowRight className="text-gray-500" />
        </div>
      </div> */}
      <div className=" relative ">
        <div className="absolute inset-0 bg-gray-100 rounded-[32px] h-[270px] top-[13%] -z-10 max-lg:hidden"></div>
        <div className="px-12 max-lg:px-0 flex flex-row items-center">
          <div
            onClick={() => {
              prevStep();
            }}
            className="h-10 w-10 min-h-10 min-w-10 rounded-full text-gray-500 bg-white hover:bg-primary hover:text-white border-2 row  justify-center cursor-pointer absolute left-0"
          >
            <ArrowLeft />
          </div>
          <CardItem
            img={steps[currentStep].img}
            title={steps[currentStep].title}
            objective={steps[currentStep].objective}
            points={steps[currentStep].points}
            currentStep={currentStep}
            length={steps.length}
          />
          <div
            onClick={() => {
              nextStep();
            }}
            className="h-10 w-10 min-h-10 min-w-10 rounded-full border-2 text-gray-500 bg-white hover:bg-primary hover:text-white  row  justify-center cursor-pointer absolute right-0"
          >
            <ArrowRight />
          </div>
        </div>
      </div>
    </div>
  );
}
