import { Button } from "@/components/ui/button";
import Link from "next/link";

const LandingWho = () => {
  return (
    <>
      <div className="max-md:-mt-10">
        <p className="heading text-center md:hidden">Who We Are</p>

        <div className="grid grid-cols-2 max-md:grid-cols-1 py-32 max-md:py-4 items-center mt-12 max-md:mt-4 ">
          <div className=" order-1  max-md:order-2">
            <p className="text-4xl max-md:text-2xl font-bold mb-5 max-md:mb-4 max-md:hidden">
              Who We Are
            </p>

            <p className="text-sec-normal mb-4">
              ArthAlpha stands at the intersection of technology and finance,
              dedicated to serve our clients through quantitative insights and
              rigorous research.{" "}
            </p>
            <p className="text-sec-normal mb-4">
              Rooted in our values of transparency, Innovation, and
              investor-centricity, we craft data-driven solutions that
              prioritize long-term growth and stability.
            </p>
            <p className="text-sec-normal mb-4">
              At ArthAlpha, we are committed to help clients achieve superior
              returns on a consistent basis.
            </p>
            <div className="row max-md:justify-center">
              <Link href="/why">
                <Button
                  className=" btn-border hover:bg-primary hover:text-white"
                  variant="outline"
                >
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
          <div className="row justify-end max-md:justify-center max-md:mb-4  order-2  max-md:order-1">
            <img
              src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1737700689/arthalpha/Frame_2018775067_hyt1be.svg"
              alt=""
              className="w-[80%] max-md:w-full justify-end flex flex-row rounded-lg"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default LandingWho;
