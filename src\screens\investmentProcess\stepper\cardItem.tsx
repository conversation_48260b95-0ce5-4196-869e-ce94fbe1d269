import { Card, CardContent } from "@/components/ui/card";

const CardItem = (item: any) => {
  return (
    <>
      <Card
        //   key={index}
        className={`rounded-[32px] max-md:w-[100%] bg-[#e9e9e9] card-item max-md:rounded-[30px] shadow-none border-1 border-[#0000000f] min-h-[330px]`}
      >
        <CardContent className="p-3 max-md:p-2 max-lg:p-4">
          <div className="w-full p-5 bg-white rounded-[24px] max-md:p-4 box-shadow">
            <div className="grid grid-cols-2 gap-4 max-md:gap-2 max-md:grid-cols-1">
              <div className="flex flex-col gap-3 items-start justify-between max-md:justify-start  md:min-h-[330px] md:border-r-[1px]">
                <div className="">
                  <img
                    src={
                      item.img
                        ? item.img
                        : "https://res.cloudinary.com/dbz8cdpis/image/upload/v1733773535/arthalpha/img2_m2my8m.svg"
                    }
                    // sizes="40px"
                    alt="behance Logo"
                    className="w-[50px] h-[50px] min-w-[60px] min-h-[60px]  mb-3"
                  />

                  <p className="text-3xl max-md:text-lg font-bold text-primary md:pr-12">
                    {item.title}
                  </p>

                  <p className="text-lg  font-[500] text-[#808080] md:pr-20 mt-4">
                    {item.objective}
                  </p>
                </div>
                <div>
                  {/* <p className="text-[#A2A2A2] text-lg font-bold">Objective</p>
                  <p className="text-lg  font-[500]">{item.objective}</p> */}
                </div>
              </div>
              <div className="flex flex-col justify-between md:pl-4">
                <div>
                  {item.points.map((item: string, index: number) => (
                    <p
                      key={index}
                      className="text-lg  font-[500] mb-2 text-[#B5B5B5] hover:text-primary hover:font-[600]"
                    >
                      {item}
                    </p>
                  ))}
                </div>
                <div className=" flex flex-row justify-end">
                  <div className="bg-[#DDDDDD] rounded-full w-16 h-6 text-center text-base font-[500]">
                    {item.currentStep + 1} OF {item.length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default CardItem;
