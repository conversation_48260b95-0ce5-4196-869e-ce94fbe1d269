/* for navBar */

/* #container {
  margin: 0 auto;
  max-width: 890px;
} */
/* p {
  text-align: center;
} */
.toggle,
[id^="drop"] {
  display: none;
}
nav {
  margin: 0;
  padding: 0;
  /* background-color: #254441; */
  background-color: white;
  border: 2px solid #e7e7e7;
  @apply bg-white bg-opacity-70 backdrop-blur-md;
}

#logo {
  display: block;
  padding: 0 12px;
  float: left;
  font-size: 20px;
  line-height: 60px;
}

nav:after {
  content: "";
  display: table;
  clear: both;
}

nav ul {
  float: left;
  padding: 0;
  margin: 0;
  list-style: none;
  position: relative;
  color: black;
}

nav ul li {
  margin: 0px;
  display: inline-block;
  float: left;
  /* background-color: #254441; */
  color: black;
}

nav a {
  display: block;
  padding: 0 10px;
  color: black;
  font-size: 16px;
  line-height: 60px;
  text-decoration: none;
}

/* nav ul li ul li:hover {
  background: #000000;
}

nav a:hover {
  background-color: #000000;
} */

nav ul ul {
  display: none;
  position: absolute;
  top: 60px;
  @apply rounded-2xl shadow-lg max-md:shadow-none;
}

nav ul li:hover > ul {
  display: inherit;
}

nav ul ul li {
  /* width: 400px; */
  float: none;
  display: list-item;
  position: relative;
  background-color: white;
  /* padding: 0px; */
  /* height: 40px; */
  color: #254441;
  @apply border-b-2 text-nowrap text-red-700;
}

nav ul ul ul li {
  position: relative;
  top: -60px;
  left: 320px;
  background-color: rebeccapurple;
}

li > a:after {
  content: " +";
  /* rotate: 45deg;
  @apply rotate-45; */
}

li > a:only-child:after {
  content: "";
}

/* Media Queries
  --------------------------------------------- */

@media all and (max-width: 768px) {
  #logo {
    display: block;
    padding: 0;
    width: 100%;
    text-align: center;
    float: none;
  }

  nav {
    margin: 0;
  }

  .toggle + a,
  .menu {
    display: none;
  }

  .toggle {
    display: block;
    /* background-color: #254441; */
    padding: 0 20px;
    /* color: #fff; */
    font-size: 16px;
    font-weight: bold;
    line-height: 60px;
    text-decoration: none;
    border: none;
  }

  .toggle:hover {
    /* background-color: #000000; */
  }

  [id^="drop"]:checked + ul {
    display: block;
  }

  nav ul li {
    display: block;
    width: 100%;
  }

  nav ul ul .toggle,
  nav ul ul a {
    padding: 0 10px;
  }

  nav ul ul ul a {
    padding: 0 20px;
  }

  nav a:hover,
  nav ul ul ul a {
    /* background-color: #000000; */
  }

  nav ul li ul li .toggle,
  nav ul ul a {
    /* background-color: #212121; */
  }

  nav ul ul {
    float: none;
    position: static;
    /* color: #ffffff; */
  }

  nav ul ul li:hover > ul,
  nav ul li:hover > ul {
    display: none;
  }

  nav ul ul li {
    display: block;
    width: 100%;
  }

  nav ul ul ul li {
    position: static;
  }
}

@media all and (max-width: 330px) {
  nav ul li {
    display: block;
    width: 94%;
    /* background-color: ; */
  }
}
