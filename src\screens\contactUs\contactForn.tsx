"use client";

import React, { useState } from "react";
import "react-phone-input-2/lib/style.css";
import PhoneInput from "react-phone-input-2";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Mail, MapPin, Phone, Loader2 } from "lucide-react";
import { ToastContainer, toast } from "react-toastify";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  ModalHeader,
  useDisclosure,
} from "@nextui-org/react";
import Link from "next/link";

const ContactForm = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [orgName, setOrgName] = useState("");
  const [mobile, setMobile] = useState("");
  const [message, setMessage] = useState("");
  const [status, setStatus] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
  }>({});
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const notify = (message: any) => toast(message ? message : "working");

  const validateForm = () => {
    let newErrors: { [key: string]: string } = {};

    if (!firstName.trim()) newErrors.firstName = "First Name is required";
    if (!lastName.trim()) newErrors.lastName = "Last Name is required";
    if (!email.trim()) newErrors.email = "Email is required";
    else if (!/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(email))
      newErrors.email = "Invalid email format";
    if (!mobile.trim()) newErrors.mobile = "Mobile number is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if valid
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Run validation before submitting
    if (!validateForm()) {
      notify("Please fix the errors before submitting.");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${API_BASE_URL}/send-email`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email,
          orgName,
          mobile,
          message,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setStatus("Email sent successfully!");
        notify(result?.success);
        if (result?.success) {
          onOpen();
        }

        // Reset form fields after successful submission
        setFirstName("");
        setLastName("");
        setEmail("");
        setOrgName("");
        setMobile("");
        setMessage("");

        // Clear validation errors after successful submission
        setErrors({});
      } else {
        notify(result?.error);
        setStatus(result.error || "Failed to send email.");
      }
    } catch (error) {
      console.error("Error:", error);
      setStatus("An error occurred.");
      notify("An error occurred while submitting the form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row bg-white p-3 md:p-4 rounded-3xl border-[1px] gap-4">
        <Card className="bg-primary text-white p-6 max-md:p-4 md:w-1/3 flex flex-col justify-between rounded-2xl">
          <div>
            <h2 className="text-3xl max-md:text-2xl font-[600] mb-1">
              Enquire Now
            </h2>
            <h3 className="text-xl max-md:text-lg mb-6">
              Drop in a message and we will reach out to you.
            </h3>
          </div>
          <div className="mb-4">
            <a
              href="https://api.whatsapp.com/send?phone=918105916156&text=Hello%2C%20I%20want%20more%20info%20about%20ArthAlpha"
              target="_blank"
              rel="noopener noreferrer"
            >
              <div className="flex items-start mb-3">
                <span className="mr-2">
                  <img
                    src="assets/WhatsappLogo.svg"
                    width="28"
                    height="28"
                    alt="WhatsApp"
                  />
                </span>
                <p>+91 8105916156</p>
              </div>
            </a>
            <a href="mailto:<EMAIL>">
              <div className="flex items-center mb-3">
                <span className="mr-2">
                  <Mail />
                </span>
                <p><EMAIL></p>
              </div>
            </a>
            <div className="flex items-start">
              <span className="mr-2">
                <MapPin />
              </span>
              <p>
                #2053 Prestige White Meadows, <br />
                Whitefield, Bangalore, 560066
              </p>
            </div>
          </div>
          <div className="grid grid-cols-2 mt-6">
            <a
              href="https://www.linkedin.com/company/arthalpha/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white text-2xl row justify-center"
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734524021/arthalpha/link_ylv8rp.svg"
                alt="LinkedIn"
              />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="text-white text-2xl row justify-center"
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734524021/arthalpha/EnvelopeSimple_kkb8bc.svg"
                alt="Email"
              />
            </a>
          </div>
        </Card>

        <form
          className="md:w-2/3 p-6 space-y-6 max-md:p-2"
          onSubmit={handleSubmit}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block mb-2 font-[600]">First Name</label>
              <input
                type="text"
                placeholder="Write here"
                className="h-11 bg-[#FAFAFA] border rounded-md p-2 w-full"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                disabled={isSubmitting}
              />
              {errors.firstName && (
                <p className="text-red-500 text-sm">{errors.firstName}</p>
              )}
            </div>
            <div>
              <label className="block mb-2 font-[600]">Last Name</label>
              <input
                type="text"
                placeholder="Write here"
                className="h-11 bg-[#FAFAFA] border rounded-md p-2 w-full"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                disabled={isSubmitting}
              />
              {errors.lastName && (
                <p className="text-red-500 text-sm">{errors.lastName}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block mb-2 font-[600]">Email</label>
              <input
                type="email"
                placeholder="Write here"
                className="h-11 bg-[#FAFAFA] border rounded-md p-2 w-full"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email}</p>
              )}
            </div>
            <div>
              <label className="block mb-2 font-[600]">Organization Name</label>
              <input
                type="text"
                placeholder="Write here"
                className="h-11 bg-[#FAFAFA] border rounded-md p-2 w-full"
                value={orgName}
                onChange={(e) => setOrgName(e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block mb-2 font-[600]">Mobile Number</label>
              <PhoneInput
                country={"in"}
                inputClass="min-h-11 min-w-full bg-[#FAFAFA] border rounded-md"
                value={mobile}
                onChange={(value) => setMobile(value)}
                inputStyle={{ borderColor: "#e5e7eb" }}
                disabled={isSubmitting}
              />
              {errors.mobile && (
                <p className="text-red-500 text-sm">{errors.mobile}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block mb-2 font-[600]">
              Message <span className="text-[#C0C0C0]">(Optional)</span>
            </label>
            <textarea
              placeholder="Write here"
              className="h-24 bg-[#FAFAFA] border rounded-md p-2 w-full"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              disabled={isSubmitting}
            />
          </div>

          <button
            type="submit"
            className="bg-primary hover:bg-[#5A0014] text-white w-full md:w-auto font-bold py-2 px-4 rounded flex items-center justify-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              "Submit"
            )}
          </button>
        </form>
        <ToastContainer />
      </div>
      <div>
        <Modal
          isDismissable={false}
          isKeyboardDismissDisabled={true}
          isOpen={isOpen}
          onOpenChange={onOpenChange}
          backdrop="blur"
          placement="center"
          className="w-[80%] h-[80%] min-w-[80%] min-h-[80%] max-md:w-[95%] max-md:h-[70%] max-md:min-w-[95%] max-md:min-h-[70%] rounded-[3rem]"
        >
          <ModalContent className="">
            {(onClose) => (
              <>
                <ModalBody className="rounded-[3rem]" style={{ padding: 0 }}>
                  <div className="relative rounded-[3rem] w-full h-full py-12 max-md:py-6 flex items-center justify-center primary-gradient">
                    <div className="absolute top-6 right-0 w-64 h-64 rounded-bl-full rounded-[3rem]">
                      <img
                        src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/Gradient_zcuwdx.svg"
                        alt=""
                        width="200"
                        height="200"
                        className="w-72 h-72"
                      />
                    </div>

                    <div className="relative bg-white-7 border border-white/100 backdrop-blur-md rounded-[3rem] px-6 py-12 max-md:py-10 max-md:mx-8 text-center max-w-lg w-full text-white shadow-lg">
                      <h2 className="text-3xl font-bold">THANK YOU!</h2>

                      <div className="flex justify-center mt-4">
                        <img
                          src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1739360337/arthalpha/image_26_i4zdma.png"
                          alt=""
                          width="200"
                          height="200"
                        />
                      </div>

                      <p className="mt-4 text-lg px-12">
                        Thank you for filling up the form!
                        <br />
                        We will be in touch and contact you soon.
                      </p>

                      <Link href="/">
                        <Button className="text-black bg-white hover:bg-white hover:text-black rounded-xl font-bold h-9 mt-4">
                          Back Home
                        </Button>
                      </Link>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ContactForm;
