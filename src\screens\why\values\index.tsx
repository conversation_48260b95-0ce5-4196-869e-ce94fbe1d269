import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const Values = () => {
  return (
    <>
      <div className="mt-32 max-md:mt-12">
        <p className="heading text-center mb-12 max-md:mb-6">Our Values</p>
        <div className=" grid grid-cols-3 max-lg:grid-cols-2 max-md:grid-cols-1 gap-4 ">
          <Card className="valusCard" style={{ padding: 0 }}>
            <CardContent
              className="flex flex-col justify-center items-center pt-6 px-6 max-md:px-4"
              //   style={{ padding: 0 }}
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734065253/arthalpha/Frame_1984078811_qlvtmp.svg"
                alt=""
              />
              <p className="text-2xl max-md:text-xl font-bold mt-3">
                {" "}
                Innovation
              </p>
              <p className="text-center pt-4 text-lg max-md:text-base text-secondary font-[500] leading-[26px]">
                Leveraging cutting-edge tech, advanced analytics, and
                forward-thinking strategies to deliver consistent, superior
                results.
              </p>
            </CardContent>
          </Card>
          <Card className="valusCard">
            <CardContent
              className="flex flex-col justify-center items-center pt-6 px-6 max-md:px-4"
              //   style={{ padding: 0 }}
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734065253/arthalpha/Frame_1984078812_pc3uxr.svg"
                alt=""
              />
              <p className="text-2xl max-md:text-xl font-bold mt-3">
                Investor-centric
              </p>
              <p className="text-center pt-4 text-lg max-md:text-base text-secondary font-[500] leading-[26px]">
                Understanding and aligning with our investors' goals, delivering
                transparent communication & unwavering support.
              </p>
            </CardContent>
          </Card>
          <Card className="valusCard">
            <CardContent
              className="flex flex-col justify-center items-center pt-6 px-6 max-md:px-4"
              //   style={{ padding: 0 }}
            >
              <img
                src="https://res.cloudinary.com/dbz8cdpis/image/upload/v1734065253/arthalpha/Frame_1984078813_vtjcor.svg"
                alt=""
              />
              <p className="text-2xl max-md:text-xl font-bold mt-3">
                Integrity
              </p>
              <p className="text-center pt-4 text-lg max-md:text-base text-secondary font-[500] leading-[26px] mb-2">
                We prioritize ethics and transparency, building trust and
                lasting relationships by acting in our investors’ best
                interests.
              </p>
            </CardContent>
          </Card>
        </div>{" "}
      </div>
    </>
  );
};

export default Values;
